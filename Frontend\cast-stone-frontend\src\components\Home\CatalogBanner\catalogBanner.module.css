/* Catalog Banner Styles */
.catalogBanner {
  position: relative;
  padding: 8rem 0;
  background: #4a3728;
  overflow: hidden;
  margin: 4rem 0;
}

/* Background Styles */
.backgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.backgroundImage {
  object-fit: cover;
  object-position: center;
}

.backgroundOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(74, 55, 40, 0.85) 0%,
    rgba(74, 55, 40, 0.7) 50%,
    rgba(107, 78, 61, 0.8) 100%
  );
  z-index: 2;
}

/* Content Styles */
.container {
  position: relative;
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.textContent {
  color: #ffffff;
}

.subtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  color: #d4af8c;
  margin-bottom: 1rem;
  display: block;
}

.title {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  color: #ffffff;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.description {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Features */
.features {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.featureIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(212, 175, 140, 0.2);
  border-radius: 50%;
  color: #d4af8c;
  flex-shrink: 0;
}

/* CTA Section */
.ctaContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2rem;
}

.ctaButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, #d4af8c, #c19a6b);
  color: #4a3728;
  text-decoration: none;
  border-radius: 50px;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(212, 175, 140, 0.3);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(212, 175, 140, 0.4);
  background: linear-gradient(135deg, #c19a6b, #d4af8c);
}

.ctaText {
  position: relative;
  z-index: 2;
}

.ctaIcon {
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.ctaButton:hover .ctaIcon {
  transform: translateX(4px);
}

.buttonRipple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease-out;
  z-index: 1;
}

.ctaButton:active .buttonRipple {
  transform: scale(1);
}

/* Catalog Stats */
.catalogStats {
  display: flex;
  gap: 3rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.statNumber {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #d4af8c;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Decorative Elements */
.decorativeElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

.decorativeCircle {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 200px;
  height: 200px;
  border: 2px solid rgba(212, 175, 140, 0.2);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.decorativeLine {
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 150px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 140, 0.3), transparent);
  animation: slide 8s ease-in-out infinite;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slide {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(50px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .catalogBanner {
    padding: 6rem 0;
  }
  
  .content {
    gap: 3rem;
  }
  
  .title {
    font-size: 3rem;
  }
  
  .features {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .catalogBanner {
    padding: 4rem 0;
    margin: 3rem 0;
  }
  
  .container {
    padding: 0 1.5rem;
  }
  
  .content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    text-align: center;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .features {
    justify-content: center;
    gap: 1rem;
  }
  
  .ctaContainer {
    align-items: center;
  }
  
  .catalogStats {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .catalogBanner {
    padding: 3rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .features {
    flex-direction: column;
    gap: 1rem;
  }
  
  .feature {
    justify-content: center;
  }
  
  .ctaButton {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }
  
  .catalogStats {
    gap: 1.5rem;
  }
  
  .statNumber {
    font-size: 2rem;
  }
}
