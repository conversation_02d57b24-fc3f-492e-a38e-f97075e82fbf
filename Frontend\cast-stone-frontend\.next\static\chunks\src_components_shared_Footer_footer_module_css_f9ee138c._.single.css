/* [project]/src/components/shared/Footer/footer.module.css [app-client] (css) */
.footer-module__94Lf8a__footer {
  color: #fff;
  background: #4a3728;
  padding: 4rem 0 2rem;
  position: relative;
}

.footer-module__94Lf8a__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-module__94Lf8a__content {
  border-bottom: 1px solid #ffffff1a;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  display: grid;
}

.footer-module__94Lf8a__brand {
  max-width: 400px;
}

.footer-module__94Lf8a__brandName {
  color: #d4af8c;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2rem;
  font-weight: 700;
}

.footer-module__94Lf8a__brandDescription {
  color: #fffc;
  margin-bottom: 2rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

.footer-module__94Lf8a__socialLinks {
  gap: 1rem;
  display: flex;
}

.footer-module__94Lf8a__socialLink {
  color: #d4af8c;
  background: #ffffff1a;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
}

.footer-module__94Lf8a__socialLink:hover {
  color: #4a3728;
  background: #d4af8c;
  transform: translateY(-2px);
}

.footer-module__94Lf8a__linkGroup {
  flex-direction: column;
  display: flex;
}

.footer-module__94Lf8a__linkGroupTitle {
  color: #d4af8c;
  letter-spacing: -.01em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.25rem;
  font-weight: 600;
}

.footer-module__94Lf8a__linkList {
  flex-direction: column;
  gap: .75rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.footer-module__94Lf8a__link {
  color: #fffc;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 400;
  line-height: 1.4;
  text-decoration: none;
  transition: all .3s;
}

.footer-module__94Lf8a__link:hover {
  color: #d4af8c;
  transform: translateX(4px);
}

.footer-module__94Lf8a__contactInfo {
  border-top: 1px solid #ffffff1a;
  grid-column: 1 / -1;
  margin-top: 2rem;
  padding-top: 2rem;
}

.footer-module__94Lf8a__contactTitle {
  color: #d4af8c;
  letter-spacing: -.01em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.25rem;
  font-weight: 600;
}

.footer-module__94Lf8a__contactDetails {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  display: grid;
}

.footer-module__94Lf8a__contactItem {
  flex-direction: column;
  gap: .25rem;
  display: flex;
}

.footer-module__94Lf8a__contactLabel {
  color: #d4af8c;
  text-transform: uppercase;
  letter-spacing: .05em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 600;
}

.footer-module__94Lf8a__contactValue {
  color: #ffffffe6;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 400;
  line-height: 1.4;
}

.footer-module__94Lf8a__bottom {
  border-top: 1px solid #ffffff1a;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  display: flex;
}

.footer-module__94Lf8a__copyright {
  color: #ffffffb3;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 400;
}

.footer-module__94Lf8a__legalLinks {
  gap: 2rem;
  display: flex;
}

.footer-module__94Lf8a__legalLink {
  color: #ffffffb3;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 400;
  text-decoration: none;
  transition: color .3s;
}

.footer-module__94Lf8a__legalLink:hover {
  color: #d4af8c;
}

@media (width <= 1024px) {
  .footer-module__94Lf8a__footer {
    padding: 3rem 0 1.5rem;
  }

  .footer-module__94Lf8a__container {
    padding: 0 1.5rem;
  }

  .footer-module__94Lf8a__content {
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
  }

  .footer-module__94Lf8a__brand {
    text-align: center;
    grid-column: 1 / -1;
    max-width: none;
    margin-bottom: 1rem;
  }

  .footer-module__94Lf8a__socialLinks {
    justify-content: center;
  }
}

@media (width <= 768px) {
  .footer-module__94Lf8a__footer {
    padding: 2.5rem 0 1rem;
  }

  .footer-module__94Lf8a__container {
    padding: 0 1rem;
  }

  .footer-module__94Lf8a__content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-module__94Lf8a__brand {
    margin-bottom: 1.5rem;
  }

  .footer-module__94Lf8a__brandName {
    font-size: 1.75rem;
  }

  .footer-module__94Lf8a__brandDescription {
    font-size: .9rem;
  }

  .footer-module__94Lf8a__linkGroup {
    align-items: center;
  }

  .footer-module__94Lf8a__linkGroupTitle {
    margin-bottom: 1rem;
    font-size: 1.125rem;
  }

  .footer-module__94Lf8a__contactInfo {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .footer-module__94Lf8a__contactDetails {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .footer-module__94Lf8a__bottom {
    text-align: center;
    flex-direction: column;
    gap: 1rem;
  }

  .footer-module__94Lf8a__legalLinks {
    gap: 1rem;
  }
}

@media (width <= 480px) {
  .footer-module__94Lf8a__footer {
    padding: 2rem 0 1rem;
  }

  .footer-module__94Lf8a__brandName {
    font-size: 1.5rem;
  }

  .footer-module__94Lf8a__brandDescription {
    font-size: .85rem;
  }

  .footer-module__94Lf8a__socialLinks {
    gap: .75rem;
  }

  .footer-module__94Lf8a__socialLink {
    width: 36px;
    height: 36px;
  }

  .footer-module__94Lf8a__linkGroupTitle {
    font-size: 1rem;
  }

  .footer-module__94Lf8a__link {
    font-size: .85rem;
  }

  .footer-module__94Lf8a__contactTitle {
    font-size: 1rem;
  }

  .footer-module__94Lf8a__contactLabel {
    font-size: .8rem;
  }

  .footer-module__94Lf8a__contactValue {
    font-size: .85rem;
  }

  .footer-module__94Lf8a__legalLinks {
    flex-direction: column;
    gap: .5rem;
  }
}

/*# sourceMappingURL=src_components_shared_Footer_footer_module_css_f9ee138c._.single.css.map*/