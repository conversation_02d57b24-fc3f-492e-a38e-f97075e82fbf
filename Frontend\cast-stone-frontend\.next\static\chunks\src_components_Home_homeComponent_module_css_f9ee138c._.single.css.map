{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/homeComponent.module.css"], "sourcesContent": ["/* Home Component Styles */\n.homeComponent {\n  min-height: 100vh;\n  background: #ffffff;\n  overflow-x: hidden;\n}\n\n/* Smooth scrolling for the entire page */\n.homeComponent {\n  scroll-behavior: smooth;\n}\n\n/* Ensure proper spacing between sections */\n.homeComponent > * {\n  position: relative;\n  z-index: 1;\n}\n\n/* Add subtle transitions for section reveals */\n.homeComponent section {\n  opacity: 1;\n  transform: translateY(0);\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .homeComponent {\n    overflow-x: hidden;\n  }\n}\n\n/* Print styles */\n@media print {\n  .homeComponent {\n    background: white;\n    color: black;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAYA;;;;;AAMA;;;;;;AAOA;EACE;;;;;AAMF;EACE"}}]}