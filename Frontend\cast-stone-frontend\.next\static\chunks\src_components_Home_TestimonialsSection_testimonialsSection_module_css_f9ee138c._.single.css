/* [project]/src/components/Home/TestimonialsSection/testimonialsSection.module.css [app-client] (css) */
.testimonialsSection-module__2JZFAW__testimonialsSection {
  color: #fff;
  background: linear-gradient(135deg, #4a3728 0%, #6b4e3d 100%);
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

.testimonialsSection-module__2JZFAW__testimonialsSection:before {
  content: "";
  opacity: .05;
  z-index: 1;
  background: url("/images/testimonials-pattern.svg");
  position: absolute;
  inset: 0;
}

.testimonialsSection-module__2JZFAW__container {
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.testimonialsSection-module__2JZFAW__header {
  text-align: center;
  max-width: 700px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.testimonialsSection-module__2JZFAW__subtitle {
  text-transform: uppercase;
  letter-spacing: .15em;
  color: #d4af8c;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 600;
  display: block;
}

.testimonialsSection-module__2JZFAW__title {
  color: #fff;
  letter-spacing: -.02em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.testimonialsSection-module__2JZFAW__description {
  color: #ffffffe6;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.testimonialsSection-module__2JZFAW__testimonialsContainer {
  grid-template-columns: 2fr 1fr;
  align-items: center;
  gap: 4rem;
  margin-bottom: 4rem;
  display: grid;
}

.testimonialsSection-module__2JZFAW__testimonialContent {
  position: relative;
}

.testimonialsSection-module__2JZFAW__quoteIcon {
  color: #d4af8c;
  opacity: .7;
  margin-bottom: 2rem;
}

.testimonialsSection-module__2JZFAW__testimonialText {
  position: relative;
}

.testimonialsSection-module__2JZFAW__testimonialContent {
  color: #fff;
  margin-bottom: 2rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-style: italic;
  font-weight: 400;
  line-height: 1.6;
}

.testimonialsSection-module__2JZFAW__rating {
  gap: .25rem;
  margin-bottom: 1.5rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__projectInfo {
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__projectLabel {
  color: #d4af8c;
  text-transform: uppercase;
  letter-spacing: .05em;
  font-weight: 600;
}

.testimonialsSection-module__2JZFAW__projectName {
  color: #ffffffe6;
  font-weight: 400;
}

.testimonialsSection-module__2JZFAW__testimonialMeta {
  flex-direction: column;
  gap: 2rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__authorInfo {
  align-items: center;
  gap: 1.5rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__authorImage {
  border: 3px solid #d4af8c;
  border-radius: 50%;
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  position: relative;
  overflow: hidden;
}

.testimonialsSection-module__2JZFAW__authorPhoto {
  object-fit: cover;
}

.testimonialsSection-module__2JZFAW__authorDetails {
  flex: 1;
}

.testimonialsSection-module__2JZFAW__authorName {
  color: #fff;
  margin-bottom: .25rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.testimonialsSection-module__2JZFAW__authorTitle {
  color: #d4af8c;
  margin-bottom: .125rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
}

.testimonialsSection-module__2JZFAW__authorCompany {
  color: #fffc;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 400;
}

.testimonialsSection-module__2JZFAW__navigation {
  justify-content: center;
  gap: .75rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__navDot {
  cursor: pointer;
  background: #ffffff4d;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.testimonialsSection-module__2JZFAW__navDot:hover {
  background: #d4af8cb3;
  transform: scale(1.2);
}

.testimonialsSection-module__2JZFAW__navDot.testimonialsSection-module__2JZFAW__active {
  background: #d4af8c;
  transform: scale(1.3);
}

.testimonialsSection-module__2JZFAW__stats {
  border-top: 1px solid #fff3;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding-top: 3rem;
  display: grid;
}

.testimonialsSection-module__2JZFAW__stat {
  text-align: center;
}

.testimonialsSection-module__2JZFAW__statNumber {
  color: #d4af8c;
  margin-bottom: .5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  display: block;
}

.testimonialsSection-module__2JZFAW__statLabel {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 500;
}

@media (width <= 1024px) {
  .testimonialsSection-module__2JZFAW__testimonialsSection {
    padding: 6rem 0;
  }

  .testimonialsSection-module__2JZFAW__container {
    padding: 0 1.5rem;
  }

  .testimonialsSection-module__2JZFAW__title {
    font-size: 3rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialsContainer {
    gap: 3rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialContent {
    font-size: 1.25rem;
  }
}

@media (width <= 768px) {
  .testimonialsSection-module__2JZFAW__testimonialsSection {
    padding: 4rem 0;
  }

  .testimonialsSection-module__2JZFAW__header {
    margin-bottom: 3rem;
  }

  .testimonialsSection-module__2JZFAW__title {
    font-size: 2.5rem;
  }

  .testimonialsSection-module__2JZFAW__description {
    font-size: 1rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialsContainer {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialContent {
    font-size: 1.125rem;
  }

  .testimonialsSection-module__2JZFAW__authorInfo {
    justify-content: center;
  }

  .testimonialsSection-module__2JZFAW__stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (width <= 480px) {
  .testimonialsSection-module__2JZFAW__container {
    padding: 0 1rem;
  }

  .testimonialsSection-module__2JZFAW__header {
    margin-bottom: 2rem;
  }

  .testimonialsSection-module__2JZFAW__title {
    font-size: 2rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialContent {
    font-size: 1rem;
  }

  .testimonialsSection-module__2JZFAW__authorInfo {
    text-align: center;
    flex-direction: column;
    gap: 1rem;
  }

  .testimonialsSection-module__2JZFAW__authorImage {
    width: 60px;
    height: 60px;
  }

  .testimonialsSection-module__2JZFAW__stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .testimonialsSection-module__2JZFAW__statNumber {
    font-size: 2rem;
  }
}

/*# sourceMappingURL=src_components_Home_TestimonialsSection_testimonialsSection_module_css_f9ee138c._.single.css.map*/