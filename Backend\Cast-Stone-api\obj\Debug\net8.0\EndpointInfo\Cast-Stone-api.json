{"openapi": "3.0.1", "info": {"title": "Cast Stone API", "description": "A comprehensive API for Cast Stone application", "contact": {"name": "Cast Stone Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Collections": {"get": {"tags": ["Collections"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Collections"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCollectionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCollectionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCollectionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}}}}}}, "/api/Collections/{id}": {"get": {"tags": ["Collections"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}}}}}, "put": {"tags": ["Collections"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCollectionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCollectionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCollectionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseApiResponse"}}}}}}, "delete": {"tags": ["Collections"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Collections/level/{level}": {"get": {"tags": ["Collections"], "parameters": [{"name": "level", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}}}}}}, "/api/Collections/hierarchy": {"get": {"tags": ["Collections"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionHierarchyResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionHierarchyResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionHierarchyResponseIEnumerableApiResponse"}}}}}}}, "/api/Collections/published": {"get": {"tags": ["Collections"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}}}}}}, "/api/Collections/search": {"get": {"tags": ["Collections"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}}}}}}, "/api/Collections/{id}/children": {"get": {"tags": ["Collections"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponseIEnumerableApiResponse"}}}}}}}, "/api/Collections/filter": {"get": {"tags": ["Collections"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Level", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ParentCollectionId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Published", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Tag", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CollectionResponsePaginatedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CollectionResponsePaginatedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CollectionResponsePaginatedResponseApiResponse"}}}}}}}, "/api/Collections/refresh-relationships": {"post": {"tags": ["Collections"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}}}}}}, "/api/Orders": {"get": {"tags": ["Orders"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderSummaryResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderSummaryResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderSummaryResponseIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}}}}}}, "/api/Orders/{id}": {"get": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}}}}}, "delete": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Orders/{id}/status": {"patch": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Orders/{id}/cancel": {"patch": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Orders/user/{userId}": {"get": {"tags": ["Orders"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}}}}}}, "/api/Orders/email/{email}": {"get": {"tags": ["Orders"], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}}}}}}, "/api/Orders/status/{statusId}": {"get": {"tags": ["Orders"], "parameters": [{"name": "statusId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}}}}}}, "/api/Orders/pending": {"get": {"tags": ["Orders"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseIEnumerableApiResponse"}}}}}}}, "/api/Orders/recent": {"get": {"tags": ["Orders"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderSummaryResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderSummaryResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderSummaryResponseIEnumerableApiResponse"}}}}}}}, "/api/Orders/{id}/details": {"get": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponseApiResponse"}}}}}}}, "/api/Orders/revenue/total": {"get": {"tags": ["Orders"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DecimalApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DecimalApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DecimalApiResponse"}}}}}}}, "/api/Orders/revenue/range": {"get": {"tags": ["Orders"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DecimalApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DecimalApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DecimalApiResponse"}}}}}}}, "/api/Orders/filter": {"get": {"tags": ["Orders"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "StatusId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "Country", "in": "query", "schema": {"type": "string"}}, {"name": "City", "in": "query", "schema": {"type": "string"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderResponsePaginatedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderResponsePaginatedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderResponsePaginatedResponseApiResponse"}}}}}}}, "/api/Products": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}}}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseApiResponse"}}}}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Products/collection/{collectionId}": {"get": {"tags": ["Products"], "parameters": [{"name": "collectionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}}}}}}, "/api/Products/in-stock": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}}}}}}, "/api/Products/featured": {"get": {"tags": ["Products"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSummaryResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSummaryResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSummaryResponseIEnumerableApiResponse"}}}}}}}, "/api/Products/latest": {"get": {"tags": ["Products"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSummaryResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSummaryResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSummaryResponseIEnumerableApiResponse"}}}}}}}, "/api/Products/search": {"get": {"tags": ["Products"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}}}}}}, "/api/Products/price-range": {"get": {"tags": ["Products"], "parameters": [{"name": "minPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponseIEnumerableApiResponse"}}}}}}}, "/api/Products/{id}/stock": {"patch": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Products/filter": {"get": {"tags": ["Products"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CollectionId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MinStock", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxStock", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "InStock", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Tag", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductResponsePaginatedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductResponsePaginatedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductResponsePaginatedResponseApiResponse"}}}}}}}, "/api/Seed/all": {"post": {"tags": ["Seed"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Seed/statuses": {"post": {"tags": ["Seed"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Seed/admin-user": {"post": {"tags": ["Seed"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Seed/collections": {"post": {"tags": ["Seed"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Seed/products": {"post": {"tags": ["Seed"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserResponseIEnumerableApiResponse"}}}}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserResponseApiResponse"}}}}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "parameters": [{"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Role", "in": "query", "schema": {"type": "string"}}, {"name": "Active", "in": "query", "schema": {"type": "boolean"}}, {"name": "Country", "in": "query", "schema": {"type": "string"}}, {"name": "City", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CreatedAfter", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedBefore", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserResponsePaginatedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserResponsePaginatedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserResponsePaginatedResponseApiResponse"}}}}}}}, "/api/Users/<USER>/{email}": {"get": {"tags": ["Users"], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Users/<USER>/{role}": {"get": {"tags": ["Users"], "parameters": [{"name": "role", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserResponseIEnumerableApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserResponseIEnumerableApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserResponseIEnumerableApiResponse"}}}}}}}, "/api/Users/<USER>/deactivate": {"patch": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Users/<USER>/activate": {"patch": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Users/<USER>/orders": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserResponseApiResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CollectionHierarchyResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "level": {"type": "integer", "format": "int32"}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "published": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionHierarchyResponse"}, "nullable": true}, "productCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CollectionHierarchyResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionHierarchyResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CollectionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "level": {"type": "integer", "format": "int32"}, "parentCollectionId": {"type": "integer", "format": "int32", "nullable": true}, "childCollectionId": {"type": "integer", "format": "int32", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "published": {"type": "boolean"}, "createdBy": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "parentCollection": {"$ref": "#/components/schemas/CollectionResponse"}, "childCollection": {"$ref": "#/components/schemas/CollectionResponse"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}, "nullable": true}}, "additionalProperties": false}, "CollectionResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/CollectionResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CollectionResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CollectionResponsePaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionResponse"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "additionalProperties": false}, "CollectionResponsePaginatedResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/CollectionResponsePaginatedResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateCollectionRequest": {"required": ["created<PERSON>y", "level", "name"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "level": {"maximum": 3, "minimum": 1, "type": "integer", "format": "int32"}, "parentCollectionId": {"type": "integer", "format": "int32", "nullable": true}, "childCollectionId": {"type": "integer", "format": "int32", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "published": {"type": "boolean"}, "createdBy": {"maxLength": 100, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateOrderItemRequest": {"required": ["productId", "quantity"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateOrderRequest": {"required": ["email", "orderItems"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32", "nullable": true}, "email": {"maxLength": 255, "minLength": 1, "type": "string", "format": "email"}, "phoneNumber": {"maxLength": 20, "type": "string", "nullable": true}, "country": {"maxLength": 100, "type": "string", "nullable": true}, "city": {"maxLength": 100, "type": "string", "nullable": true}, "zipCode": {"maxLength": 20, "type": "string", "nullable": true}, "paymentMethod": {"maxLength": 50, "type": "string", "nullable": true}, "orderItems": {"type": "array", "items": {"$ref": "#/components/schemas/CreateOrderItemRequest"}}}, "additionalProperties": false}, "CreateProductRequest": {"required": ["collectionId", "name", "price", "stock"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "stock": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "collectionId": {"type": "integer", "format": "int32"}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateUserRequest": {"required": ["email", "password", "role"], "type": "object", "properties": {"role": {"maxLength": 20, "minLength": 1, "type": "string"}, "email": {"maxLength": 255, "minLength": 1, "type": "string", "format": "email"}, "phoneNumber": {"maxLength": 20, "type": "string", "nullable": true}, "password": {"minLength": 6, "type": "string"}, "name": {"maxLength": 100, "type": "string", "nullable": true}, "country": {"maxLength": 100, "type": "string", "nullable": true}, "city": {"maxLength": 100, "type": "string", "nullable": true}, "zipCode": {"maxLength": 20, "type": "string", "nullable": true}, "active": {"type": "boolean"}}, "additionalProperties": false}, "DecimalApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "number", "format": "double"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Int32ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "integer", "format": "int32"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderItemResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "priceAtPurchaseTime": {"type": "number", "format": "double"}, "orderId": {"type": "integer", "format": "int32"}, "product": {"$ref": "#/components/schemas/ProductResponse"}}, "additionalProperties": false}, "OrderResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zipCode": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "statusId": {"type": "integer", "format": "int32"}, "paymentMethod": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/UserResponse"}, "status": {"$ref": "#/components/schemas/StatusResponse"}, "orderItems": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemResponse"}, "nullable": true}}, "additionalProperties": false}, "OrderResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/OrderResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderResponsePaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderResponse"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "additionalProperties": false}, "OrderResponsePaginatedResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/OrderResponsePaginatedResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderSummaryResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "statusName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "itemCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OrderSummaryResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderSummaryResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProductResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "stock": {"type": "integer", "format": "int32"}, "collectionId": {"type": "integer", "format": "int32"}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "collection": {"$ref": "#/components/schemas/CollectionResponse"}}, "additionalProperties": false}, "ProductResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ProductResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProductResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProductResponsePaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "additionalProperties": false}, "ProductResponsePaginatedResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ProductResponsePaginatedResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProductSummaryResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "stock": {"type": "integer", "format": "int32"}, "mainImage": {"type": "string", "nullable": true}, "collectionName": {"type": "string", "nullable": true}, "inStock": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ProductSummaryResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSummaryResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "StatusResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "statusName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateCollectionRequest": {"required": ["level", "name", "updatedBy"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "level": {"maximum": 3, "minimum": 1, "type": "integer", "format": "int32"}, "parentCollectionId": {"type": "integer", "format": "int32", "nullable": true}, "childCollectionId": {"type": "integer", "format": "int32", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "published": {"type": "boolean"}, "updatedBy": {"maxLength": 100, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdateOrderStatusRequest": {"required": ["statusId"], "type": "object", "properties": {"statusId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateProductRequest": {"required": ["collectionId", "name", "price", "stock"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "stock": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "collectionId": {"type": "integer", "format": "int32"}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateUserRequest": {"required": ["role"], "type": "object", "properties": {"role": {"maxLength": 20, "minLength": 1, "type": "string"}, "phoneNumber": {"maxLength": 20, "type": "string", "nullable": true}, "name": {"maxLength": 100, "type": "string", "nullable": true}, "country": {"maxLength": 100, "type": "string", "nullable": true}, "city": {"maxLength": 100, "type": "string", "nullable": true}, "zipCode": {"maxLength": 20, "type": "string", "nullable": true}, "active": {"type": "boolean"}}, "additionalProperties": false}, "UserResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "role": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zipCode": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "active": {"type": "boolean"}}, "additionalProperties": false}, "UserResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserResponseIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserResponsePaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "additionalProperties": false}, "UserResponsePaginatedResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserResponsePaginatedResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}}}}