{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CategoriesSection/categoriesSection.module.css"], "sourcesContent": ["/* Categories Section Styles */\n.categoriesSection {\n  padding: 6rem 0;\n  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);\n  position: relative;\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Header Styles */\n.header {\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.sectionTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3rem;\n  font-weight: 700;\n  color: #4a3728;\n  margin-bottom: 1rem;\n  letter-spacing: -0.02em;\n}\n\n.sectionSubtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  color: #8b7355;\n  line-height: 1.6;\n  font-weight: 400;\n}\n\n/* Grid Layout */\n.grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Category Card Styles */\n.categoryCard {\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #ffffff;\n  box-shadow: 0 8px 32px rgba(74, 55, 40, 0.1);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  height: 400px;\n}\n\n.categoryCard:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 20px 60px rgba(74, 55, 40, 0.15);\n}\n\n.cardLink {\n  display: block;\n  width: 100%;\n  height: 100%;\n  text-decoration: none;\n  color: inherit;\n  position: relative;\n}\n\n/* Image Styles */\n.imageContainer {\n  position: relative;\n  height: 60%;\n  overflow: hidden;\n}\n\n.categoryImage {\n  object-fit: cover;\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.categoryCard:hover .categoryImage {\n  transform: scale(1.05);\n}\n\n.imageOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.3) 0%,\n    rgba(0, 0, 0, 0.1) 50%,\n    rgba(74, 55, 40, 0.4) 100%\n  );\n  z-index: 1;\n}\n\n/* Card Content */\n.cardContent {\n  position: relative;\n  height: 40%;\n  padding: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  z-index: 2;\n}\n\n.cardHeader {\n  margin-bottom: 1rem;\n}\n\n.stats {\n  display: flex;\n  align-items: baseline;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.statsNumber {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #4a3728;\n  line-height: 1;\n}\n\n.statsLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.75rem;\n  font-weight: 500;\n  color: #8b7355;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.categoryTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #4a3728;\n  margin: 0.25rem 0;\n  line-height: 1.2;\n}\n\n.categorySubtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.8rem;\n  font-weight: 600;\n  color: #8b7355;\n  text-transform: uppercase;\n  letter-spacing: 0.15em;\n  margin: 0;\n}\n\n.categoryDescription {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: #6b4e3d;\n  line-height: 1.5;\n  margin-bottom: 1rem;\n  flex-grow: 1;\n}\n\n/* Card Actions */\n.cardActions {\n  display: flex;\n  gap: 0.75rem;\n  align-items: center;\n}\n\n.actionButton,\n.secondaryButton {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.actionButton {\n  background: #4a3728;\n  color: #ffffff;\n  padding: 0.5rem 1rem;\n}\n\n.actionButton:hover {\n  background: #6b4e3d;\n  transform: translateY(-1px);\n}\n\n.secondaryButton {\n  background: transparent;\n  color: #8b7355;\n  padding: 0.5rem 0.75rem;\n  border: 1px solid rgba(139, 115, 85, 0.3);\n}\n\n.secondaryButton:hover {\n  background: rgba(139, 115, 85, 0.1);\n  color: #4a3728;\n}\n\n/* Hover Effect */\n.hoverEffect {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(74, 55, 40, 0.05) 0%,\n    transparent 50%,\n    rgba(139, 115, 85, 0.05) 100%\n  );\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  z-index: 1;\n}\n\n.categoryCard:hover .hoverEffect {\n  opacity: 1;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .categoriesSection {\n    padding: 4rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .sectionTitle {\n    font-size: 2.5rem;\n  }\n  \n  .grid {\n    gap: 1.5rem;\n  }\n  \n  .categoryCard {\n    height: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .categoryCard {\n    height: 300px;\n  }\n  \n  .sectionTitle {\n    font-size: 2rem;\n  }\n  \n  .sectionSubtitle {\n    font-size: 1rem;\n  }\n  \n  .cardContent {\n    padding: 1.25rem;\n  }\n  \n  .statsNumber {\n    font-size: 1.75rem;\n  }\n  \n  .categoryTitle {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .categoriesSection {\n    padding: 3rem 0;\n  }\n  \n  .container {\n    padding: 0 1rem;\n  }\n  \n  .header {\n    margin-bottom: 2.5rem;\n  }\n  \n  .categoryCard {\n    height: 280px;\n  }\n  \n  .cardContent {\n    padding: 1rem;\n  }\n  \n  .cardActions {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .actionButton,\n  .secondaryButton {\n    width: 100%;\n    justify-content: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;AAgBA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA"}}]}