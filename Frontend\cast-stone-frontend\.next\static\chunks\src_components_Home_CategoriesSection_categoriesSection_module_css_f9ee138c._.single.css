/* [project]/src/components/Home/CategoriesSection/categoriesSection.module.css [app-client] (css) */
.categoriesSection-module__fGUJra__categoriesSection {
  background: linear-gradient(135deg, #faf9f7 0%, #fff 100%);
  padding: 6rem 0;
  position: relative;
}

.categoriesSection-module__fGUJra__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.categoriesSection-module__fGUJra__header {
  text-align: center;
  max-width: 800px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.categoriesSection-module__fGUJra__sectionTitle {
  color: #4a3728;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3rem;
  font-weight: 700;
}

.categoriesSection-module__fGUJra__sectionSubtitle {
  color: #8b7355;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.categoriesSection-module__fGUJra__grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.categoriesSection-module__fGUJra__categoryCard {
  background: #fff;
  border-radius: 16px;
  height: 400px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #4a37281a;
}

.categoriesSection-module__fGUJra__categoryCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px #4a372826;
}

.categoriesSection-module__fGUJra__cardLink {
  width: 100%;
  height: 100%;
  color: inherit;
  text-decoration: none;
  display: block;
  position: relative;
}

.categoriesSection-module__fGUJra__imageContainer {
  height: 60%;
  position: relative;
  overflow: hidden;
}

.categoriesSection-module__fGUJra__categoryImage {
  object-fit: cover;
  transition: transform .6s cubic-bezier(.4, 0, .2, 1);
}

.categoriesSection-module__fGUJra__categoryCard:hover .categoriesSection-module__fGUJra__categoryImage {
  transform: scale(1.05);
}

.categoriesSection-module__fGUJra__imageOverlay {
  z-index: 1;
  background: linear-gradient(135deg, #0000004d 0%, #0000001a 50%, #4a372866 100%);
  position: absolute;
  inset: 0;
}

.categoriesSection-module__fGUJra__cardContent {
  z-index: 2;
  flex-direction: column;
  justify-content: space-between;
  height: 40%;
  padding: 1.5rem;
  display: flex;
  position: relative;
}

.categoriesSection-module__fGUJra__cardHeader {
  margin-bottom: 1rem;
}

.categoriesSection-module__fGUJra__stats {
  align-items: baseline;
  gap: .5rem;
  margin-bottom: .5rem;
  display: flex;
}

.categoriesSection-module__fGUJra__statsNumber {
  color: #4a3728;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.categoriesSection-module__fGUJra__statsLabel {
  color: #8b7355;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .75rem;
  font-weight: 500;
}

.categoriesSection-module__fGUJra__categoryTitle {
  color: #4a3728;
  margin: .25rem 0;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.categoriesSection-module__fGUJra__categorySubtitle {
  color: #8b7355;
  text-transform: uppercase;
  letter-spacing: .15em;
  margin: 0;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 600;
}

.categoriesSection-module__fGUJra__categoryDescription {
  color: #6b4e3d;
  flex-grow: 1;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  line-height: 1.5;
}

.categoriesSection-module__fGUJra__cardActions {
  align-items: center;
  gap: .75rem;
  display: flex;
}

.categoriesSection-module__fGUJra__actionButton, .categoriesSection-module__fGUJra__secondaryButton {
  text-transform: uppercase;
  letter-spacing: .05em;
  cursor: pointer;
  border: none;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 600;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
}

.categoriesSection-module__fGUJra__actionButton {
  color: #fff;
  background: #4a3728;
  padding: .5rem 1rem;
}

.categoriesSection-module__fGUJra__actionButton:hover {
  background: #6b4e3d;
  transform: translateY(-1px);
}

.categoriesSection-module__fGUJra__secondaryButton {
  color: #8b7355;
  background: none;
  border: 1px solid #8b73554d;
  padding: .5rem .75rem;
}

.categoriesSection-module__fGUJra__secondaryButton:hover {
  color: #4a3728;
  background: #8b73551a;
}

.categoriesSection-module__fGUJra__hoverEffect {
  opacity: 0;
  z-index: 1;
  background: linear-gradient(135deg, #4a37280d 0%, #0000 50%, #8b73550d 100%);
  transition: opacity .3s;
  position: absolute;
  inset: 0;
}

.categoriesSection-module__fGUJra__categoryCard:hover .categoriesSection-module__fGUJra__hoverEffect {
  opacity: 1;
}

@media (width <= 1024px) {
  .categoriesSection-module__fGUJra__categoriesSection {
    padding: 4rem 0;
  }

  .categoriesSection-module__fGUJra__container {
    padding: 0 1.5rem;
  }

  .categoriesSection-module__fGUJra__sectionTitle {
    font-size: 2.5rem;
  }

  .categoriesSection-module__fGUJra__grid {
    gap: 1.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 350px;
  }
}

@media (width <= 768px) {
  .categoriesSection-module__fGUJra__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 300px;
  }

  .categoriesSection-module__fGUJra__sectionTitle {
    font-size: 2rem;
  }

  .categoriesSection-module__fGUJra__sectionSubtitle {
    font-size: 1rem;
  }

  .categoriesSection-module__fGUJra__cardContent {
    padding: 1.25rem;
  }

  .categoriesSection-module__fGUJra__statsNumber {
    font-size: 1.75rem;
  }

  .categoriesSection-module__fGUJra__categoryTitle {
    font-size: 1.25rem;
  }
}

@media (width <= 480px) {
  .categoriesSection-module__fGUJra__categoriesSection {
    padding: 3rem 0;
  }

  .categoriesSection-module__fGUJra__container {
    padding: 0 1rem;
  }

  .categoriesSection-module__fGUJra__header {
    margin-bottom: 2.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 280px;
  }

  .categoriesSection-module__fGUJra__cardContent {
    padding: 1rem;
  }

  .categoriesSection-module__fGUJra__cardActions {
    flex-direction: column;
    gap: .5rem;
  }

  .categoriesSection-module__fGUJra__actionButton, .categoriesSection-module__fGUJra__secondaryButton {
    justify-content: center;
    width: 100%;
  }
}

/*# sourceMappingURL=src_components_Home_CategoriesSection_categoriesSection_module_css_f9ee138c._.single.css.map*/