{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/TestimonialsSection/testimonialsSection.module.css"], "sourcesContent": ["/* Testimonials Section Styles */\n.testimonialsSection {\n  padding: 8rem 0;\n  background: linear-gradient(135deg, #4a3728 0%, #6b4e3d 100%);\n  color: #ffffff;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonialsSection::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('/images/testimonials-pattern.svg') repeat;\n  opacity: 0.05;\n  z-index: 1;\n}\n\n.container {\n  position: relative;\n  z-index: 2;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Header Styles */\n.header {\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.15em;\n  color: #d4af8c;\n  margin-bottom: 1rem;\n  display: block;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin-bottom: 1.5rem;\n  letter-spacing: -0.02em;\n  line-height: 1.1;\n}\n\n.description {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n/* Testimonials Container */\n.testimonialsContainer {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 4rem;\n  align-items: center;\n  margin-bottom: 4rem;\n}\n\n/* Testimonial Content */\n.testimonialContent {\n  position: relative;\n}\n\n.quoteIcon {\n  color: #d4af8c;\n  margin-bottom: 2rem;\n  opacity: 0.7;\n}\n\n.testimonialText {\n  position: relative;\n}\n\n.testimonialContent {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  line-height: 1.6;\n  color: #ffffff;\n  margin-bottom: 2rem;\n  font-style: italic;\n  font-weight: 400;\n}\n\n.rating {\n  display: flex;\n  gap: 0.25rem;\n  margin-bottom: 1.5rem;\n}\n\n.projectInfo {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n}\n\n.projectLabel {\n  color: #d4af8c;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.projectName {\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n/* Testimonial Meta */\n.testimonialMeta {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.authorInfo {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n\n.authorImage {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  overflow: hidden;\n  border: 3px solid #d4af8c;\n  flex-shrink: 0;\n}\n\n.authorPhoto {\n  object-fit: cover;\n}\n\n.authorDetails {\n  flex: 1;\n}\n\n.authorName {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 0.25rem;\n  line-height: 1.2;\n}\n\n.authorTitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: #d4af8c;\n  font-weight: 500;\n  margin-bottom: 0.125rem;\n}\n\n.authorCompany {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 400;\n}\n\n/* Navigation */\n.navigation {\n  display: flex;\n  gap: 0.75rem;\n  justify-content: center;\n}\n\n.navDot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.navDot:hover {\n  background: rgba(212, 175, 140, 0.7);\n  transform: scale(1.2);\n}\n\n.navDot.active {\n  background: #d4af8c;\n  transform: scale(1.3);\n}\n\n/* Stats */\n.stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 2rem;\n  padding-top: 3rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.stat {\n  text-align: center;\n}\n\n.statNumber {\n  display: block;\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #d4af8c;\n  line-height: 1;\n  margin-bottom: 0.5rem;\n}\n\n.statLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .testimonialsSection {\n    padding: 6rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .title {\n    font-size: 3rem;\n  }\n  \n  .testimonialsContainer {\n    gap: 3rem;\n  }\n  \n  .testimonialContent {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .testimonialsSection {\n    padding: 4rem 0;\n  }\n  \n  .header {\n    margin-bottom: 3rem;\n  }\n  \n  .title {\n    font-size: 2.5rem;\n  }\n  \n  .description {\n    font-size: 1rem;\n  }\n  \n  .testimonialsContainer {\n    grid-template-columns: 1fr;\n    gap: 2.5rem;\n    text-align: center;\n  }\n  \n  .testimonialContent {\n    font-size: 1.125rem;\n  }\n  \n  .authorInfo {\n    justify-content: center;\n  }\n  \n  .stats {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 1rem;\n  }\n  \n  .header {\n    margin-bottom: 2rem;\n  }\n  \n  .title {\n    font-size: 2rem;\n  }\n  \n  .testimonialContent {\n    font-size: 1rem;\n  }\n  \n  .authorInfo {\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n  \n  .authorImage {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .stats {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .statNumber {\n    font-size: 2rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;AAYA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA"}}]}