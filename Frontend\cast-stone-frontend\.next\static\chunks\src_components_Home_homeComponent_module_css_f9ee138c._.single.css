/* [project]/src/components/Home/homeComponent.module.css [app-client] (css) */
.homeComponent-module__wtPNwW__homeComponent {
  scroll-behavior: smooth;
  background: #fff;
  min-height: 100vh;
  overflow-x: hidden;
}

.homeComponent-module__wtPNwW__homeComponent > * {
  z-index: 1;
  position: relative;
}

.homeComponent-module__wtPNwW__homeComponent section {
  opacity: 1;
  transition: all .6s cubic-bezier(.4, 0, .2, 1);
  transform: translateY(0);
}

@media (width <= 768px) {
  .homeComponent-module__wtPNwW__homeComponent {
    overflow-x: hidden;
  }
}

@media print {
  .homeComponent-module__wtPNwW__homeComponent {
    color: #000;
    background: #fff;
  }
}

/*# sourceMappingURL=src_components_Home_homeComponent_module_css_f9ee138c._.single.css.map*/