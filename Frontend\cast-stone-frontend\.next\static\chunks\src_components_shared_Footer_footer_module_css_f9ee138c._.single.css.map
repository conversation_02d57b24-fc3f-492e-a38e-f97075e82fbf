{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Footer/footer.module.css"], "sourcesContent": ["/* Footer Styles */\n.footer {\n  background: #4a3728;\n  color: #ffffff;\n  padding: 4rem 0 2rem;\n  position: relative;\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Content Grid */\n.content {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr;\n  gap: 3rem;\n  margin-bottom: 3rem;\n  padding-bottom: 3rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* Brand Section */\n.brand {\n  max-width: 400px;\n}\n\n.brandName {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #d4af8c;\n  margin-bottom: 1rem;\n  letter-spacing: -0.02em;\n}\n\n.brandDescription {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 2rem;\n  font-weight: 400;\n}\n\n/* Social Links */\n.socialLinks {\n  display: flex;\n  gap: 1rem;\n}\n\n.socialLink {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n  color: #d4af8c;\n  text-decoration: none;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.socialLink:hover {\n  background: #d4af8c;\n  color: #4a3728;\n  transform: translateY(-2px);\n}\n\n/* Link Groups */\n.linkGroup {\n  display: flex;\n  flex-direction: column;\n}\n\n.linkGroupTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #d4af8c;\n  margin-bottom: 1.5rem;\n  letter-spacing: -0.01em;\n}\n\n.linkList {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.link {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.8);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  font-weight: 400;\n  line-height: 1.4;\n}\n\n.link:hover {\n  color: #d4af8c;\n  transform: translateX(4px);\n}\n\n/* Contact Info */\n.contactInfo {\n  grid-column: 1 / -1;\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.contactTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #d4af8c;\n  margin-bottom: 1.5rem;\n  letter-spacing: -0.01em;\n}\n\n.contactDetails {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n}\n\n.contactItem {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.contactLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  font-weight: 600;\n  color: #d4af8c;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.contactValue {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.4;\n  font-weight: 400;\n}\n\n/* Bottom Section */\n.bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 2rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.copyright {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  color: rgba(255, 255, 255, 0.7);\n  font-weight: 400;\n}\n\n.legalLinks {\n  display: flex;\n  gap: 2rem;\n}\n\n.legalLink {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-decoration: none;\n  transition: color 0.3s ease;\n  font-weight: 400;\n}\n\n.legalLink:hover {\n  color: #d4af8c;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .footer {\n    padding: 3rem 0 1.5rem;\n  }\n\n  .container {\n    padding: 0 1.5rem;\n  }\n\n  .content {\n    grid-template-columns: 1fr 1fr;\n    gap: 2.5rem;\n  }\n\n  .brand {\n    grid-column: 1 / -1;\n    max-width: none;\n    text-align: center;\n    margin-bottom: 1rem;\n  }\n\n  .socialLinks {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 768px) {\n  .footer {\n    padding: 2.5rem 0 1rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  .content {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n    text-align: center;\n  }\n\n  .brand {\n    margin-bottom: 1.5rem;\n  }\n\n  .brandName {\n    font-size: 1.75rem;\n  }\n\n  .brandDescription {\n    font-size: 0.9rem;\n  }\n\n  .linkGroup {\n    align-items: center;\n  }\n\n  .linkGroupTitle {\n    font-size: 1.125rem;\n    margin-bottom: 1rem;\n  }\n\n  .contactInfo {\n    margin-top: 1.5rem;\n    padding-top: 1.5rem;\n    text-align: center;\n  }\n\n  .contactDetails {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    text-align: center;\n  }\n\n  .bottom {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n\n  .legalLinks {\n    gap: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .footer {\n    padding: 2rem 0 1rem;\n  }\n\n  .brandName {\n    font-size: 1.5rem;\n  }\n\n  .brandDescription {\n    font-size: 0.85rem;\n  }\n\n  .socialLinks {\n    gap: 0.75rem;\n  }\n\n  .socialLink {\n    width: 36px;\n    height: 36px;\n  }\n\n  .linkGroupTitle {\n    font-size: 1rem;\n  }\n\n  .link {\n    font-size: 0.85rem;\n  }\n\n  .contactTitle {\n    font-size: 1rem;\n  }\n\n  .contactLabel {\n    font-size: 0.8rem;\n  }\n\n  .contactValue {\n    font-size: 0.85rem;\n  }\n\n  .legalLinks {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}