import React from 'react';
import Link from 'next/link';
import styles from './footer.module.css';

interface FooterProps {
  companyName?: string;
}

const Footer: React.FC<FooterProps> = ({ companyName = "Cast Stone" }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Brand Section */}
          <div className={styles.brand}>
            <h3 className={styles.brandName}>{companyName}</h3>
            <p className={styles.brandDescription}>
              Creating timeless beauty with handcrafted cast stone elements for over 25 years.
            </p>

            <div className={styles.socialLinks}>
              <a href="#" className={styles.socialLink} aria-label="Facebook">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" className={styles.socialLink} aria-label="Instagram">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <a href="#" className={styles.socialLink} aria-label="LinkedIn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Company Links */}
          <div className={styles.linkGroup}>
            <h4 className={styles.linkGroupTitle}>Company</h4>
            <ul className={styles.linkList}>
              <li><Link href="/contact" className={styles.link}>Contact Us</Link></li>
              <li><Link href="/our-story" className={styles.link}>Our Story</Link></li>
              <li><Link href="/retail-locator" className={styles.link}>Retail Locator</Link></li>
              <li><Link href="/wholesale-signup" className={styles.link}>Wholesale Sign-up</Link></li>
            </ul>
          </div>

          {/* Products Links */}
          <div className={styles.linkGroup}>
            <h4 className={styles.linkGroupTitle}>Products</h4>
            <ul className={styles.linkList}>
              <li><Link href="/products/architectural" className={styles.link}>Architectural Products</Link></li>
              <li><Link href="/products/designer" className={styles.link}>Designer Products</Link></li>
              <li><Link href="/products/limited-edition" className={styles.link}>Limited Edition</Link></li>
              <li><Link href="/products/sealers" className={styles.link}>Cast Stone Sealers</Link></li>
            </ul>
          </div>

          {/* Discover Links */}
          <div className={styles.linkGroup}>
            <h4 className={styles.linkGroupTitle}>Discover</h4>
            <ul className={styles.linkList}>
              <li><Link href="/catalog" className={styles.link}>Catalog</Link></li>
              <li><Link href="/collections" className={styles.link}>Collections</Link></li>
              <li><Link href="/completed-projects" className={styles.link}>Completed Projects</Link></li>
              <li><Link href="/videos" className={styles.link}>Videos</Link></li>
              <li><Link href="/faq" className={styles.link}>FAQs</Link></li>
            </ul>
          </div>
        </div>

        {/* Contact Info */}
        <div className={styles.contactInfo}>
          <h4 className={styles.contactTitle}>Contact Info</h4>
          <div className={styles.contactDetails}>
            <div className={styles.contactItem}>
              <span className={styles.contactLabel}>Address:</span>
              <span className={styles.contactValue}>
                123 Artisan Way<br />
                Craftsman City, CC 12345
              </span>
            </div>
            <div className={styles.contactItem}>
              <span className={styles.contactLabel}>Phone:</span>
              <span className={styles.contactValue}>(*************</span>
            </div>
            <div className={styles.contactItem}>
              <span className={styles.contactLabel}>Email:</span>
              <span className={styles.contactValue}><EMAIL></span>
            </div>
          </div>
        </div>

        <div className={styles.bottom}>
          <p className={styles.copyright}>
            © {currentYear} {companyName}. All rights reserved.
          </p>
          <div className={styles.legalLinks}>
            <Link href="/privacy" className={styles.legalLink}>Privacy Policy</Link>
            <Link href="/terms" className={styles.legalLink}>Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
