{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css"], "sourcesContent": ["/* Collections Carousel Styles */\n.collectionsSection {\n  padding: 6rem 0;\n  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Header Styles */\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  margin-bottom: 3rem;\n  gap: 2rem;\n}\n\n.headerContent {\n  flex: 1;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3rem;\n  font-weight: 700;\n  color: #4a3728;\n  margin-bottom: 1rem;\n  letter-spacing: -0.02em;\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  color: #8b7355;\n  line-height: 1.6;\n  font-weight: 400;\n  max-width: 600px;\n}\n\n/* Navigation */\n.navigation {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.navButton {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  background: #ffffff;\n  border: 2px solid rgba(74, 55, 40, 0.1);\n  border-radius: 50%;\n  color: #4a3728;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 4px 12px rgba(74, 55, 40, 0.1);\n}\n\n.navButton:hover {\n  background: #4a3728;\n  color: #ffffff;\n  border-color: #4a3728;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(74, 55, 40, 0.2);\n}\n\n/* Carousel Container */\n.carouselContainer {\n  position: relative;\n  margin-bottom: 3rem;\n}\n\n.carousel {\n  display: flex;\n  gap: 1.5rem;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  padding: 1rem 0;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.carousel::-webkit-scrollbar {\n  display: none;\n}\n\n/* Collection Card */\n.collectionCard {\n  flex: 0 0 320px;\n  height: 400px;\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #ffffff;\n  box-shadow: 0 8px 32px rgba(74, 55, 40, 0.1);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.collectionCard:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 20px 60px rgba(74, 55, 40, 0.15);\n}\n\n.cardLink {\n  display: block;\n  width: 100%;\n  height: 100%;\n  text-decoration: none;\n  color: inherit;\n}\n\n/* Image Styles */\n.imageContainer {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.collectionImage {\n  object-fit: cover;\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.collectionCard:hover .collectionImage {\n  transform: scale(1.05);\n}\n\n.imageOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.3) 0%,\n    rgba(0, 0, 0, 0.1) 50%,\n    rgba(74, 55, 40, 0.6) 100%\n  );\n  z-index: 1;\n}\n\n/* Card Content */\n.cardContent {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 2rem;\n  color: #ffffff;\n  z-index: 2;\n  background: linear-gradient(\n    to top,\n    rgba(0, 0, 0, 0.8) 0%,\n    rgba(0, 0, 0, 0.4) 50%,\n    transparent 100%\n  );\n}\n\n.productCount {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.8rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n  color: #d4af8c;\n  margin-bottom: 0.5rem;\n}\n\n.collectionName {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 0.75rem;\n  line-height: 1.2;\n}\n\n.collectionDescription {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.4;\n  margin-bottom: 1rem;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.cardAction {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  color: #d4af8c;\n  transition: all 0.3s ease;\n}\n\n.collectionCard:hover .cardAction {\n  color: #ffffff;\n  transform: translateX(4px);\n}\n\n.actionText {\n  transition: transform 0.3s ease;\n}\n\n/* View All Button */\n.viewAllContainer {\n  text-align: center;\n}\n\n.viewAllButton {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem 2rem;\n  background: transparent;\n  color: #4a3728;\n  text-decoration: none;\n  border: 2px solid #4a3728;\n  border-radius: 50px;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.viewAllButton:hover {\n  background: #4a3728;\n  color: #ffffff;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(74, 55, 40, 0.3);\n}\n\n/* Loading and Error States */\n.loadingContainer,\n.errorContainer {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #8b7355;\n}\n\n.loadingSpinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(74, 55, 40, 0.1);\n  border-top: 3px solid #4a3728;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .collectionsSection {\n    padding: 4rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .title {\n    font-size: 2.5rem;\n  }\n  \n  .collectionCard {\n    flex: 0 0 280px;\n    height: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1.5rem;\n  }\n  \n  .navigation {\n    align-self: flex-end;\n  }\n  \n  .title {\n    font-size: 2rem;\n  }\n  \n  .subtitle {\n    font-size: 1rem;\n  }\n  \n  .collectionCard {\n    flex: 0 0 260px;\n    height: 320px;\n  }\n  \n  .cardContent {\n    padding: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 1rem;\n  }\n  \n  .header {\n    margin-bottom: 2rem;\n  }\n  \n  .navigation {\n    display: none;\n  }\n  \n  .collectionCard {\n    flex: 0 0 240px;\n    height: 300px;\n  }\n  \n  .cardContent {\n    padding: 1.25rem;\n  }\n  \n  .collectionName {\n    font-size: 1.25rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;AAgBA;;;;;;;;;;;AAgBA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA"}}]}