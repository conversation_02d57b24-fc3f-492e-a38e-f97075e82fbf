{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\collection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\collection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\createcollectionrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\createcollectionrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\response\\collectionresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\response\\collectionresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\scripts\\seeddata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:scripts\\seeddata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\repositories\\implementations\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:repositories\\implementations\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\services\\implementations\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:services\\implementations\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\createuserrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\createuserrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\updateuserrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\updateuserrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\response\\userresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\response\\userresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\controllers\\collectionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:controllers\\collectionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\repositories\\implementations\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:repositories\\implementations\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\services\\implementations\\collectionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:services\\implementations\\collectionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\repositories\\implementations\\collectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:repositories\\implementations\\collectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\cast-stone-api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:cast-stone-api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Collection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Collection.cs", "RelativeDocumentMoniker": "Domain\\Models\\Collection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Collection.cs", "RelativeToolTip": "Domain\\Models\\Collection.cs", "ViewState": "AgIAABEAAAAAAAAAAAAwwBgAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:49:16.169Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CreateCollectionRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateCollectionRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\CreateCollectionRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateCollectionRequest.cs", "RelativeToolTip": "DTOs\\Request\\CreateCollectionRequest.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAUwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:49:07.302Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CollectionResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Response\\CollectionResponse.cs", "RelativeDocumentMoniker": "DTOs\\Response\\CollectionResponse.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Response\\CollectionResponse.cs", "RelativeToolTip": "DTOs\\Response\\CollectionResponse.cs", "ViewState": "AgIAABIAAAAAAAAAAAAzwCIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:48:58.546Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "SeedData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Scripts\\SeedData.cs", "RelativeDocumentMoniker": "Scripts\\SeedData.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Scripts\\SeedData.cs", "RelativeToolTip": "Scripts\\SeedData.cs", "ViewState": "AgIAADoAAAAAAAAAAIA6wFEAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:33:23.225Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Data\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Data\\ApplicationDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Data\\ApplicationDbContext.cs", "RelativeToolTip": "Data\\ApplicationDbContext.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAkwCcAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:31:17.389Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UserService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\Implementations\\UserService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\UserService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\Implementations\\UserService.cs", "RelativeToolTip": "Services\\Implementations\\UserService.cs", "ViewState": "AgIAABMAAAAAAAAAAAAuwCEAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:32:05.785Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CreateUserRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateUserRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\CreateUserRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateUserRequest.cs", "RelativeToolTip": "DTOs\\Request\\CreateUserRequest.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwBUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:31:54.043Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UpdateUserRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\UpdateUserRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\UpdateUserRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\UpdateUserRequest.cs", "RelativeToolTip": "DTOs\\Request\\UpdateUserRequest.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:31:37.464Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UserRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\UserRepository.cs", "RelativeDocumentMoniker": "Repositories\\Implementations\\UserRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\UserRepository.cs", "RelativeToolTip": "Repositories\\Implementations\\UserRepository.cs", "ViewState": "AgIAAHIAAAAAAAAAAAArwHYAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:33:01.008Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "UserResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Response\\UserResponse.cs", "RelativeDocumentMoniker": "DTOs\\Response\\UserResponse.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Response\\UserResponse.cs", "RelativeToolTip": "DTOs\\Response\\UserResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:30:52.656Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "UsersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "Controllers\\UsersController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\UsersController.cs", "RelativeToolTip": "Controllers\\UsersController.cs", "ViewState": "AgIAAA0AAAAAAAAAAAA1wBwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:30:43.407Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "User.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\User.cs", "RelativeDocumentMoniker": "Domain\\Models\\User.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\User.cs", "RelativeToolTip": "Domain\\Models\\User.cs", "ViewState": "AgIAABkAAAAAAAAAAAAcwBcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:30:21.302Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "BaseRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\BaseRepository.cs", "RelativeDocumentMoniker": "Repositories\\Implementations\\BaseRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\BaseRepository.cs", "RelativeToolTip": "Repositories\\Implementations\\BaseRepository.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAQwC4AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:10:38.343Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "CollectionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\CollectionRepository.cs", "RelativeDocumentMoniker": "Repositories\\Implementations\\CollectionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\CollectionRepository.cs", "RelativeToolTip": "Repositories\\Implementations\\CollectionRepository.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAcwBMAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:15:24.134Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "CollectionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\Implementations\\CollectionService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\CollectionService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\Implementations\\CollectionService.cs", "RelativeToolTip": "Services\\Implementations\\CollectionService.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAgwCsAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:10:01.708Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "CollectionsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\CollectionsController.cs", "RelativeDocumentMoniker": "Controllers\\CollectionsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\CollectionsController.cs", "RelativeToolTip": "Controllers\\CollectionsController.cs", "ViewState": "AgIAADQAAAAAAAAAAAApwEYAAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:08:29.526Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-03T21:16:16.723Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Cast-<PERSON>-api", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Cast-Stone-api.csproj", "RelativeDocumentMoniker": "Cast-Stone-api.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Cast-Stone-api.csproj", "RelativeToolTip": "Cast-Stone-api.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-03T22:24:19.288Z", "EditorCaption": ""}]}, {"DockedWidth": 68, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}]}]}]}