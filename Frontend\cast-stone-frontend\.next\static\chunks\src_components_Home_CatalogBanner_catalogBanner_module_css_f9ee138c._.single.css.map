{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CatalogBanner/catalogBanner.module.css"], "sourcesContent": ["/* Catalog Banner Styles */\n.catalogBanner {\n  position: relative;\n  padding: 8rem 0;\n  background: #4a3728;\n  overflow: hidden;\n  margin: 4rem 0;\n}\n\n/* Background Styles */\n.backgroundContainer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1;\n}\n\n.backgroundImage {\n  object-fit: cover;\n  object-position: center;\n}\n\n.backgroundOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(74, 55, 40, 0.85) 0%,\n    rgba(74, 55, 40, 0.7) 50%,\n    rgba(107, 78, 61, 0.8) 100%\n  );\n  z-index: 2;\n}\n\n/* Content Styles */\n.container {\n  position: relative;\n  z-index: 3;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 4rem;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.textContent {\n  color: #ffffff;\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.15em;\n  color: #d4af8c;\n  margin-bottom: 1rem;\n  display: block;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3.5rem;\n  font-weight: 700;\n  line-height: 1.1;\n  color: #ffffff;\n  margin-bottom: 1.5rem;\n  letter-spacing: -0.02em;\n}\n\n.description {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2.5rem;\n  font-weight: 400;\n}\n\n/* Features */\n.features {\n  display: flex;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.feature {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.featureIcon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: rgba(212, 175, 140, 0.2);\n  border-radius: 50%;\n  color: #d4af8c;\n  flex-shrink: 0;\n}\n\n/* CTA Section */\n.ctaContainer {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.ctaButton {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.25rem 2.5rem;\n  background: linear-gradient(135deg, #d4af8c, #c19a6b);\n  color: #4a3728;\n  text-decoration: none;\n  border-radius: 50px;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(212, 175, 140, 0.3);\n}\n\n.ctaButton:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 40px rgba(212, 175, 140, 0.4);\n  background: linear-gradient(135deg, #c19a6b, #d4af8c);\n}\n\n.ctaText {\n  position: relative;\n  z-index: 2;\n}\n\n.ctaIcon {\n  position: relative;\n  z-index: 2;\n  transition: transform 0.3s ease;\n}\n\n.ctaButton:hover .ctaIcon {\n  transform: translateX(4px);\n}\n\n.buttonRipple {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\n  transform: scale(0);\n  transition: transform 0.6s ease-out;\n  z-index: 1;\n}\n\n.ctaButton:active .buttonRipple {\n  transform: scale(1);\n}\n\n/* Catalog Stats */\n.catalogStats {\n  display: flex;\n  gap: 3rem;\n}\n\n.stat {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.statNumber {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #d4af8c;\n  line-height: 1;\n  margin-bottom: 0.25rem;\n}\n\n.statLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.8);\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* Decorative Elements */\n.decorativeElements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 2;\n  pointer-events: none;\n}\n\n.decorativeCircle {\n  position: absolute;\n  top: 20%;\n  right: 10%;\n  width: 200px;\n  height: 200px;\n  border: 2px solid rgba(212, 175, 140, 0.2);\n  border-radius: 50%;\n  animation: float 6s ease-in-out infinite;\n}\n\n.decorativeLine {\n  position: absolute;\n  bottom: 20%;\n  left: 5%;\n  width: 150px;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, rgba(212, 175, 140, 0.3), transparent);\n  animation: slide 8s ease-in-out infinite;\n}\n\n/* Animations */\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes slide {\n  0%, 100% {\n    transform: translateX(0px);\n  }\n  50% {\n    transform: translateX(50px);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .catalogBanner {\n    padding: 6rem 0;\n  }\n  \n  .content {\n    gap: 3rem;\n  }\n  \n  .title {\n    font-size: 3rem;\n  }\n  \n  .features {\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .catalogBanner {\n    padding: 4rem 0;\n    margin: 3rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .content {\n    grid-template-columns: 1fr;\n    gap: 2.5rem;\n    text-align: center;\n  }\n  \n  .title {\n    font-size: 2.5rem;\n  }\n  \n  .description {\n    font-size: 1rem;\n  }\n  \n  .features {\n    justify-content: center;\n    gap: 1rem;\n  }\n  \n  .ctaContainer {\n    align-items: center;\n  }\n  \n  .catalogStats {\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .catalogBanner {\n    padding: 3rem 0;\n  }\n  \n  .container {\n    padding: 0 1rem;\n  }\n  \n  .title {\n    font-size: 2rem;\n  }\n  \n  .features {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .feature {\n    justify-content: center;\n  }\n  \n  .ctaButton {\n    padding: 1rem 2rem;\n    font-size: 0.9rem;\n  }\n  \n  .catalogStats {\n    gap: 1.5rem;\n  }\n  \n  .statNumber {\n    font-size: 2rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AASA;;;;;;AASA;;;;;AAKA;;;;;;;AAgBA;;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA"}}]}