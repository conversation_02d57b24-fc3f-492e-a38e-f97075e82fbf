/* Hero Section Styles */
.hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #1a1a1a; /* Fallback color */
}

/* Video Background */
.videoContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.backgroundVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 2;
}

/* Content Styles */
.container {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #d4af8c;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.02em;
}

.titleLine1,
.titleLine2 {
  display: block;
  animation: fadeInUp 1s ease-out forwards;
}

.titleLine1 {
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(30px);
}

.titleLine2 {
  animation-delay: 0.6s;
  opacity: 0;
  transform: translateY(30px);
}

.subtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.6;
  color: #ffffff;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  animation: fadeInUp 1s ease-out 0.9s forwards;
  opacity: 0;
  transform: translateY(30px);
}

/* Button Styles */
.actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 1.2s forwards;
  opacity: 0;
  transform: translateY(30px);
}

.primaryButton,
.secondaryButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2.5rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  min-width: 200px;
}

.primaryButton {
  background: linear-gradient(135deg, #8b4513, #a0522d);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.primaryButton:hover {
  background: linear-gradient(135deg, #a0522d, #8b4513);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
}

.secondaryButton {
  background: transparent;
  color: #ffffff;
  border-color: #ffffff;
  backdrop-filter: blur(10px);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.buttonText {
  position: relative;
  z-index: 2;
}

.buttonRipple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease-out;
  z-index: 1;
}

.primaryButton:active .buttonRipple,
.secondaryButton:active .buttonRipple {
  transform: scale(1);
}

/* Scroll Indicator */
.scrollIndicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  animation: fadeInUp 1s ease-out 1.5s forwards;
  opacity: 0;
}

.scrollArrow {
  color: #ffffff;
  animation: bounce 2s infinite;
  cursor: pointer;
  transition: color 0.3s ease;
}

.scrollArrow:hover {
  color: #d4af8c;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .title {
    font-size: 3.5rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 2rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .hero {
    min-height: 500px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .primaryButton,
  .secondaryButton {
    font-size: 0.8rem;
    padding: 0.75rem 1.5rem;
  }
}

/* Video fallback for mobile devices */
@media (max-width: 768px) {
  .backgroundVideo {
    display: none;
  }

  .hero {
    background: linear-gradient(
      135deg,
      rgba(26, 26, 26, 0.9) 0%,
      rgba(74, 55, 40, 0.8) 50%,
      rgba(26, 26, 26, 0.9) 100%
    ),
    url('/images/hero-mobile-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
  }
}
