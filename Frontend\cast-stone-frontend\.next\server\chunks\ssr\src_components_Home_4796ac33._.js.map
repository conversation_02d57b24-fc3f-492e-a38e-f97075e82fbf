{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/HeroSection/heroSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actions\": \"heroSection-module__CcYIFG__actions\",\n  \"backgroundVideo\": \"heroSection-module__CcYIFG__backgroundVideo\",\n  \"bounce\": \"heroSection-module__CcYIFG__bounce\",\n  \"buttonRipple\": \"heroSection-module__CcYIFG__buttonRipple\",\n  \"buttonText\": \"heroSection-module__CcYIFG__buttonText\",\n  \"container\": \"heroSection-module__CcYIFG__container\",\n  \"content\": \"heroSection-module__CcYIFG__content\",\n  \"fadeInUp\": \"heroSection-module__CcYIFG__fadeInUp\",\n  \"hero\": \"heroSection-module__CcYIFG__hero\",\n  \"primaryButton\": \"heroSection-module__CcYIFG__primaryButton\",\n  \"scrollArrow\": \"heroSection-module__CcYIFG__scrollArrow\",\n  \"scrollIndicator\": \"heroSection-module__C<PERSON><PERSON><PERSON>G__scrollIndicator\",\n  \"secondaryButton\": \"heroSection-module__CcYIFG__secondaryButton\",\n  \"subtitle\": \"heroSection-module__CcYIFG__subtitle\",\n  \"title\": \"heroSection-module__CcYIFG__title\",\n  \"titleLine1\": \"heroSection-module__CcYIFG__titleLine1\",\n  \"titleLine2\": \"heroSection-module__CcYIFG__titleLine2\",\n  \"videoContainer\": \"heroSection-module__CcYIFG__videoContainer\",\n  \"videoOverlay\": \"heroSection-module__CcYIFG__videoOverlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport styles from './heroSection.module.css';\n\ninterface HeroSectionProps {\n  title?: string;\n  subtitle?: string;\n  videoSrc?: string;\n}\n\nconst HeroSection: React.FC<HeroSectionProps> = ({\n  title = \"Timeless Elegance in Cast Stone\",\n  subtitle = \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\",\n  videoSrc = \"/videos/hero-background.mp4\" // Default video path\n}) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    // Ensure video plays automatically and loops\n    if (videoRef.current) {\n      videoRef.current.play().catch(error => {\n        console.log('Video autoplay failed:', error);\n      });\n    }\n  }, []);\n\n  return (\n    <section className={styles.hero}>\n      {/* Video Background */}\n      <div className={styles.videoContainer}>\n        <video\n          ref={videoRef}\n          className={styles.backgroundVideo}\n          autoPlay\n          muted\n          loop\n          playsInline\n          poster=\"/images/hero-poster.jpg\" // Fallback image\n        >\n          <source src={videoSrc} type=\"video/mp4\" />\n          <source src=\"/videos/hero-background.webm\" type=\"video/webm\" />\n          {/* Fallback for browsers that don't support video */}\n          Your browser does not support the video tag.\n        </video>\n\n        {/* Video Overlay */}\n        <div className={styles.videoOverlay}></div>\n      </div>\n\n      {/* Content */}\n      <div className={styles.container}>\n        <div className={styles.content}>\n          <h1 className={styles.title}>\n            <span className={styles.titleLine1}>Timeless Elegance in</span>\n            <span className={styles.titleLine2}>Cast Stone</span>\n          </h1>\n\n          <p className={styles.subtitle}>\n            {subtitle}\n          </p>\n\n          <div className={styles.actions}>\n            <Link href=\"/collections\" className={styles.primaryButton}>\n              <span className={styles.buttonText}>EXPLORE COLLECTION</span>\n              <div className={styles.buttonRipple}></div>\n            </Link>\n\n            <Link href=\"/our-story\" className={styles.secondaryButton}>\n              <span className={styles.buttonText}>WATCH OUR STORY</span>\n              <div className={styles.buttonRipple}></div>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className={styles.scrollIndicator}>\n        <div className={styles.scrollArrow}>\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n            <path d=\"M7 13L12 18L17 13\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            <path d=\"M7 6L12 11L17 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n          </svg>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,iCAAiC,EACzC,WAAW,qJAAqJ,EAChK,WAAW,8BAA8B,qBAAqB;AAAtB,EACzC;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;gBAC5B,QAAQ,GAAG,CAAC,0BAA0B;YACxC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAW,mKAAA,CAAA,UAAM,CAAC,IAAI;;0BAE7B,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,8OAAC;wBACC,KAAK;wBACL,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;wBACjC,QAAQ;wBACR,KAAK;wBACL,IAAI;wBACJ,WAAW;wBACX,QAAO,0BAA0B,iBAAiB;;;0CAElD,8OAAC;gCAAO,KAAK;gCAAU,MAAK;;;;;;0CAC5B,8OAAC;gCAAO,KAAI;gCAA+B,MAAK;;;;;;4BACM;;;;;;;kCAKxD,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;0BAIrC,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,SAAS;0BAC9B,cAAA,8OAAC;oBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;sCAC5B,8OAAC;4BAAG,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;;8CACzB,8OAAC;oCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;;;;;;;sCAGtC,8OAAC;4BAAE,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;sCAC1B;;;;;;sCAGH,8OAAC;4BAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;8CAC5B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAW,mKAAA,CAAA,UAAM,CAAC,aAAa;;sDACvD,8OAAC;4CAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sDAAE;;;;;;sDACpC,8OAAC;4CAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;8CAGrC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;;sDACvD,8OAAC;4CAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sDAAE;;;;;;sDACpC,8OAAC;4CAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;0BACpC,cAAA,8OAAC;oBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,WAAW;8BAChC,cAAA,8OAAC;wBAAI,OAAM;wBAAK,QAAO;wBAAK,SAAQ;wBAAY,MAAK;;0CACnD,8OAAC;gCAAK,GAAE;gCAAoB,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;;;;;0CACvG,8OAAC;gCAAK,GAAE;gCAAkB,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjH;uCAEe", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/FeaturesSection/featuresSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/FeaturesSection/FeaturesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport styles from './featuresSection.module.css';\n\ninterface Feature {\n  id: string;\n  title: string;\n  description: string;\n  icon?: string;\n}\n\ninterface FeaturesSectionProps {\n  title?: string;\n  features?: Feature[];\n}\n\nconst defaultFeatures: Feature[] = [\n  {\n    id: '1',\n    title: 'Modern Architecture',\n    description: 'Built with the latest technologies and best practices for scalability and performance.',\n    icon: '🏗️'\n  },\n  {\n    id: '2',\n    title: 'Responsive Design',\n    description: 'Fully responsive design that works seamlessly across all devices and screen sizes.',\n    icon: '📱'\n  },\n  {\n    id: '3',\n    title: 'Fast Performance',\n    description: 'Optimized for speed with efficient code and modern development practices.',\n    icon: '⚡'\n  }\n];\n\nconst FeaturesSection: React.FC<FeaturesSectionProps> = ({\n  title = \"Why Choose Cast Stone?\",\n  features = defaultFeatures\n}) => {\n  return (\n    <section className={styles.featuresSection}>\n      <div className={styles.container}>\n        <h2 className={styles.title}>{title}</h2>\n        <div className={styles.featuresGrid}>\n          {features.map((feature) => (\n            <div key={feature.id} className={styles.featureCard}>\n              {feature.icon && (\n                <div className={styles.icon}>{feature.icon}</div>\n              )}\n              <h3 className={styles.featureTitle}>{feature.title}</h3>\n              <p className={styles.featureDescription}>{feature.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AACA;;;AAcA,MAAM,kBAA6B;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAED,MAAM,kBAAkD,CAAC,EACvD,QAAQ,wBAAwB,EAChC,WAAW,eAAe,EAC3B;IACC,qBACE,8OAAC;QAAQ,WAAW,2KAAA,CAAA,UAAM,CAAC,eAAe;kBACxC,cAAA,8OAAC;YAAI,WAAW,2KAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAG,WAAW,2KAAA,CAAA,UAAM,CAAC,KAAK;8BAAG;;;;;;8BAC9B,8OAAC;oBAAI,WAAW,2KAAA,CAAA,UAAM,CAAC,YAAY;8BAChC,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAAqB,WAAW,2KAAA,CAAA,UAAM,CAAC,WAAW;;gCAChD,QAAQ,IAAI,kBACX,8OAAC;oCAAI,WAAW,2KAAA,CAAA,UAAM,CAAC,IAAI;8CAAG,QAAQ,IAAI;;;;;;8CAE5C,8OAAC;oCAAG,WAAW,2KAAA,CAAA,UAAM,CAAC,YAAY;8CAAG,QAAQ,KAAK;;;;;;8CAClD,8OAAC;oCAAE,WAAW,2KAAA,CAAA,UAAM,CAAC,kBAAkB;8CAAG,QAAQ,WAAW;;;;;;;2BALrD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAYhC;uCAEe", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/homeComponent.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"homeComponent\": \"homeComponent-module__wtPNwW__homeComponent\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HomeComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport HeroSection from './HeroSection/HeroSection';\nimport FeaturesSection from './FeaturesSection/FeaturesSection';\nimport styles from './homeComponent.module.css';\n\nconst HomeComponent: React.FC = () => {\n  const handleGetStarted = () => {\n    // Navigate to getting started page or scroll to features\n    const featuresSection = document.getElementById('features');\n    if (featuresSection) {\n      featuresSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <div className={styles.homeComponent}>\n      <HeroSection \n        title=\"Welcome to Cast Stone\"\n        subtitle=\"Building the future with modern technology and innovative solutions\"\n        ctaText=\"Explore Features\"\n        onCtaClick={handleGetStarted}\n      />\n      <div id=\"features\">\n        <FeaturesSection />\n      </div>\n    </div>\n  );\n};\n\nexport default HomeComponent;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOA,MAAM,gBAA0B;IAC9B,MAAM,mBAAmB;QACvB,yDAAyD;QACzD,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,sJAAA,CAAA,UAAM,CAAC,aAAa;;0BAClC,8OAAC,wJAAA,CAAA,UAAW;gBACV,OAAM;gBACN,UAAS;gBACT,SAAQ;gBACR,YAAY;;;;;;0BAEd,8OAAC;gBAAI,IAAG;0BACN,cAAA,8OAAC,gKAAA,CAAA,UAAe;;;;;;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}]}