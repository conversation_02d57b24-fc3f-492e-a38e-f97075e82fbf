/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-900: oklch(21% .034 264.665);
    --color-white: #fff;
    --spacing: .25rem;
    --container-4xl: 56rem;
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .mx-auto {
    margin-inline: auto;
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .block {
    display: block;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-full {
    width: 100%;
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .text-center {
    text-align: center;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-white {
    color: var(--color-white);
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  .focus\:border-blue-500:focus {
    border-color: var(--color-blue-500);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}


/* [project]/src/components/shared/Header/header.module.css [app-client] (css) */
:root {
  --cast-stone-brown: #4a3728;
  --cast-stone-light-brown: #6b4e3d;
  --cast-stone-cream: #faf9f7;
  --cast-stone-white: #fff;
  --cast-stone-gray: #8b7355;
  --cast-stone-shadow: #4a37281a;
  --cast-stone-shadow-hover: #4a372826;
  --transition-smooth: all .3s cubic-bezier(.4, 0, .2, 1);
  --transition-fast: all .2s cubic-bezier(.4, 0, .2, 1);
}

.header-module__zhIT0W__header {
  background-color: var(--cast-stone-white);
  z-index: 1000;
  box-shadow: 0 2px 8px var(--cast-stone-shadow);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #4a372814;
  padding: 0;
  position: sticky;
  top: 0;
}

.header-module__zhIT0W__container {
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  height: 80px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
}

.header-module__zhIT0W__logo {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.header-module__zhIT0W__logoLink {
  color: var(--cast-stone-brown);
  transition: var(--transition-smooth);
  flex-direction: column;
  align-items: flex-start;
  text-decoration: none;
  display: flex;
}

.header-module__zhIT0W__logoLink:hover {
  transform: translateY(-1px);
}

.header-module__zhIT0W__logoText {
  letter-spacing: -.02em;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.header-module__zhIT0W__logoSubtext {
  letter-spacing: .1em;
  text-transform: uppercase;
  color: var(--cast-stone-gray);
  margin-top: 2px;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .75rem;
  font-weight: 400;
}

.header-module__zhIT0W__nav {
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__navList {
  align-items: center;
  gap: 0;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.header-module__zhIT0W__navItem {
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
  color: var(--cast-stone-brown);
  transition: var(--transition-smooth);
  cursor: pointer;
  letter-spacing: .01em;
  background: none;
  border: none;
  align-items: center;
  gap: .5rem;
  padding: 1.5rem 1.25rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .95rem;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__navLink:hover, .header-module__zhIT0W__navButton:hover {
  color: var(--cast-stone-light-brown);
  transform: translateY(-1px);
}

.header-module__zhIT0W__navLink:after, .header-module__zhIT0W__navButton:after {
  content: "";
  background: linear-gradient(90deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  width: 0;
  height: 2px;
  transition: var(--transition-smooth);
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.header-module__zhIT0W__navLink:hover:after, .header-module__zhIT0W__navButton:hover:after, .header-module__zhIT0W__navButton.header-module__zhIT0W__active:after {
  width: 80%;
}

.header-module__zhIT0W__dropdownContainer {
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__dropdownIcon {
  transition: var(--transition-smooth);
  color: var(--cast-stone-gray);
  justify-content: center;
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__dropdownIcon.header-module__zhIT0W__rotated {
  color: var(--cast-stone-brown);
  transform: rotate(180deg);
}

.header-module__zhIT0W__loadingIcon {
  color: var(--cast-stone-gray);
  justify-content: center;
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__dropdown {
  background: var(--cast-stone-white);
  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  min-width: 220px;
  transition: var(--transition-smooth);
  backdrop-filter: blur(10px);
  border: 1px solid #4a37281a;
  border-radius: 8px;
  animation: .3s cubic-bezier(.4, 0, .2, 1) forwards header-module__zhIT0W__dropdownSlideIn;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%)translateY(-10px);
}

@keyframes header-module__zhIT0W__dropdownSlideIn {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%)translateY(-10px);
  }

  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%)translateY(0);
  }
}

.header-module__zhIT0W__dropdownList {
  margin: 0;
  padding: .5rem 0;
  list-style: none;
}

.header-module__zhIT0W__dropdownItem {
  position: relative;
}

.header-module__zhIT0W__dropdownLink {
  color: var(--cast-stone-brown);
  transition: var(--transition-fast);
  border-left: 3px solid #0000;
  padding: .75rem 1.25rem;
  font-size: .9rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
}

.header-module__zhIT0W__dropdownLink:hover {
  color: var(--cast-stone-light-brown);
  border-left-color: var(--cast-stone-brown);
  background: #4a37280a;
  transform: translateX(2px);
}

.header-module__zhIT0W__subDropdownList {
  background: #4a372805;
  border-top: 1px solid #4a372814;
  margin: 0;
  padding: 0;
  list-style: none;
}

.header-module__zhIT0W__subDropdownItem {
  position: relative;
}

.header-module__zhIT0W__subDropdownLink {
  color: var(--cast-stone-gray);
  transition: var(--transition-fast);
  border-left: 3px solid #0000;
  padding: .6rem 1.25rem .6rem 2rem;
  font-size: .85rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
  position: relative;
}

.header-module__zhIT0W__subDropdownLink:before {
  content: "→";
  color: var(--cast-stone-gray);
  transition: var(--transition-fast);
  font-size: .7rem;
  position: absolute;
  left: 1.5rem;
}

.header-module__zhIT0W__subDropdownLink:hover {
  color: var(--cast-stone-brown);
  border-left-color: var(--cast-stone-light-brown);
  background: #4a37280f;
  transform: translateX(2px);
}

.header-module__zhIT0W__subDropdownLink:hover:before {
  color: var(--cast-stone-brown);
  transform: translateX(2px);
}

.header-module__zhIT0W__cartContainer {
  align-items: center;
  margin-left: 1rem;
  display: flex;
}

.header-module__zhIT0W__cartLink {
  width: 44px;
  height: 44px;
  color: var(--cast-stone-brown);
  transition: var(--transition-smooth);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__cartLink:hover {
  color: var(--cast-stone-light-brown);
  background: #4a37280f;
  transform: translateY(-1px);
}

@media (width <= 1024px) {
  .header-module__zhIT0W__container {
    padding: 0 1.5rem;
  }

  .header-module__zhIT0W__navList {
    gap: 0;
  }

  .header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
    padding: 1.5rem 1rem;
    font-size: .9rem;
  }
}

@media (width <= 768px) {
  .header-module__zhIT0W__container {
    height: 70px;
    padding: 0 1rem;
  }

  .header-module__zhIT0W__logoText {
    font-size: 1.75rem;
  }

  .header-module__zhIT0W__logoSubtext {
    font-size: .7rem;
  }

  .header-module__zhIT0W__navList {
    gap: 0;
  }

  .header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
    padding: 1.25rem .75rem;
    font-size: .85rem;
  }

  .header-module__zhIT0W__dropdown {
    min-width: 200px;
  }
}

@media (width <= 640px) {
  .header-module__zhIT0W__nav {
    display: none;
  }

  .header-module__zhIT0W__container {
    justify-content: space-between;
  }
}


/* [project]/src/components/shared/Footer/footer.module.css [app-client] (css) */



/*# sourceMappingURL=%5Broot-of-the-server%5D__87a6e4c6._.css.map*/