{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/heroSection.module.css"], "sourcesContent": ["/* Hero Section Styles */\n.hero {\n  position: relative;\n  height: 100vh;\n  min-height: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background-color: #1a1a1a; /* Fallback color */\n}\n\n/* Video Background */\n.videoContainer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.backgroundVideo {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  object-position: center;\n}\n\n.videoOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.6) 0%,\n    rgba(0, 0, 0, 0.4) 50%,\n    rgba(0, 0, 0, 0.7) 100%\n  );\n  z-index: 2;\n}\n\n/* Content Styles */\n.container {\n  position: relative;\n  z-index: 3;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  text-align: center;\n}\n\n.content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 4rem;\n  font-weight: 700;\n  line-height: 1.1;\n  margin-bottom: 1.5rem;\n  color: #d4af8c;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n  letter-spacing: -0.02em;\n}\n\n.titleLine1,\n.titleLine2 {\n  display: block;\n  animation: fadeInUp 1s ease-out forwards;\n}\n\n.titleLine1 {\n  animation-delay: 0.3s;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.titleLine2 {\n  animation-delay: 0.6s;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.25rem;\n  font-weight: 400;\n  line-height: 1.6;\n  color: #ffffff;\n  margin-bottom: 3rem;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);\n  animation: fadeInUp 1s ease-out 0.9s forwards;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n/* Button Styles */\n.actions {\n  display: flex;\n  gap: 1.5rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  animation: fadeInUp 1s ease-out 1.2s forwards;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.primaryButton,\n.secondaryButton {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 1rem 2.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  letter-spacing: 0.1em;\n  text-transform: uppercase;\n  text-decoration: none;\n  border-radius: 50px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  overflow: hidden;\n  cursor: pointer;\n  border: 2px solid transparent;\n  min-width: 200px;\n}\n\n.primaryButton {\n  background: linear-gradient(135deg, #8b4513, #a0522d);\n  color: #ffffff;\n  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);\n}\n\n.primaryButton:hover {\n  background: linear-gradient(135deg, #a0522d, #8b4513);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);\n}\n\n.secondaryButton {\n  background: transparent;\n  color: #ffffff;\n  border-color: #ffffff;\n  backdrop-filter: blur(10px);\n}\n\n.secondaryButton:hover {\n  background: rgba(255, 255, 255, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n.buttonText {\n  position: relative;\n  z-index: 2;\n}\n\n.buttonRipple {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\n  transform: scale(0);\n  transition: transform 0.6s ease-out;\n  z-index: 1;\n}\n\n.primaryButton:active .buttonRipple,\n.secondaryButton:active .buttonRipple {\n  transform: scale(1);\n}\n\n/* Scroll Indicator */\n.scrollIndicator {\n  position: absolute;\n  bottom: 2rem;\n  left: 50%;\n  transform: translateX(-50%);\n  z-index: 3;\n  animation: fadeInUp 1s ease-out 1.5s forwards;\n  opacity: 0;\n}\n\n.scrollArrow {\n  color: #ffffff;\n  animation: bounce 2s infinite;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.scrollArrow:hover {\n  color: #d4af8c;\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10px);\n  }\n  60% {\n    transform: translateY(-5px);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .title {\n    font-size: 3.5rem;\n  }\n\n  .subtitle {\n    font-size: 1.1rem;\n  }\n\n  .container {\n    padding: 0 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .title {\n    font-size: 2.5rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n    margin-bottom: 2rem;\n  }\n\n  .actions {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n  }\n\n  .primaryButton,\n  .secondaryButton {\n    width: 100%;\n    max-width: 280px;\n    padding: 0.875rem 2rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .hero {\n    min-height: 500px;\n  }\n\n  .title {\n    font-size: 2rem;\n    margin-bottom: 1rem;\n  }\n\n  .subtitle {\n    font-size: 0.9rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .primaryButton,\n  .secondaryButton {\n    font-size: 0.8rem;\n    padding: 0.75rem 1.5rem;\n  }\n}\n\n/* Video fallback for mobile devices */\n@media (max-width: 768px) {\n  .backgroundVideo {\n    display: none;\n  }\n\n  .hero {\n    background: linear-gradient(\n      135deg,\n      rgba(26, 26, 26, 0.9) 0%,\n      rgba(74, 55, 40, 0.8) 50%,\n      rgba(26, 26, 26, 0.9) 100%\n    ),\n    url('/images/hero-mobile-bg.jpg');\n    background-size: cover;\n    background-position: center;\n    background-attachment: fixed;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAgBA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;AAQF;EACE;;;;EAIA", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CategoriesSection/categoriesSection.module.css"], "sourcesContent": ["/* Categories Section Styles */\n.categoriesSection {\n  padding: 6rem 0;\n  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);\n  position: relative;\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Header Styles */\n.header {\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.sectionTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3rem;\n  font-weight: 700;\n  color: #4a3728;\n  margin-bottom: 1rem;\n  letter-spacing: -0.02em;\n}\n\n.sectionSubtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  color: #8b7355;\n  line-height: 1.6;\n  font-weight: 400;\n}\n\n/* Grid Layout */\n.grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Category Card Styles */\n.categoryCard {\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #ffffff;\n  box-shadow: 0 8px 32px rgba(74, 55, 40, 0.1);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  height: 400px;\n}\n\n.categoryCard:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 20px 60px rgba(74, 55, 40, 0.15);\n}\n\n.cardLink {\n  display: block;\n  width: 100%;\n  height: 100%;\n  text-decoration: none;\n  color: inherit;\n  position: relative;\n}\n\n/* Image Styles */\n.imageContainer {\n  position: relative;\n  height: 60%;\n  overflow: hidden;\n}\n\n.categoryImage {\n  object-fit: cover;\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.categoryCard:hover .categoryImage {\n  transform: scale(1.05);\n}\n\n.imageOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.3) 0%,\n    rgba(0, 0, 0, 0.1) 50%,\n    rgba(74, 55, 40, 0.4) 100%\n  );\n  z-index: 1;\n}\n\n/* Card Content */\n.cardContent {\n  position: relative;\n  height: 40%;\n  padding: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  z-index: 2;\n}\n\n.cardHeader {\n  margin-bottom: 1rem;\n}\n\n.stats {\n  display: flex;\n  align-items: baseline;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.statsNumber {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #4a3728;\n  line-height: 1;\n}\n\n.statsLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.75rem;\n  font-weight: 500;\n  color: #8b7355;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.categoryTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #4a3728;\n  margin: 0.25rem 0;\n  line-height: 1.2;\n}\n\n.categorySubtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.8rem;\n  font-weight: 600;\n  color: #8b7355;\n  text-transform: uppercase;\n  letter-spacing: 0.15em;\n  margin: 0;\n}\n\n.categoryDescription {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: #6b4e3d;\n  line-height: 1.5;\n  margin-bottom: 1rem;\n  flex-grow: 1;\n}\n\n/* Card Actions */\n.cardActions {\n  display: flex;\n  gap: 0.75rem;\n  align-items: center;\n}\n\n.actionButton,\n.secondaryButton {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.actionButton {\n  background: #4a3728;\n  color: #ffffff;\n  padding: 0.5rem 1rem;\n}\n\n.actionButton:hover {\n  background: #6b4e3d;\n  transform: translateY(-1px);\n}\n\n.secondaryButton {\n  background: transparent;\n  color: #8b7355;\n  padding: 0.5rem 0.75rem;\n  border: 1px solid rgba(139, 115, 85, 0.3);\n}\n\n.secondaryButton:hover {\n  background: rgba(139, 115, 85, 0.1);\n  color: #4a3728;\n}\n\n/* Hover Effect */\n.hoverEffect {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(74, 55, 40, 0.05) 0%,\n    transparent 50%,\n    rgba(139, 115, 85, 0.05) 100%\n  );\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  z-index: 1;\n}\n\n.categoryCard:hover .hoverEffect {\n  opacity: 1;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .categoriesSection {\n    padding: 4rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .sectionTitle {\n    font-size: 2.5rem;\n  }\n  \n  .grid {\n    gap: 1.5rem;\n  }\n  \n  .categoryCard {\n    height: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .categoryCard {\n    height: 300px;\n  }\n  \n  .sectionTitle {\n    font-size: 2rem;\n  }\n  \n  .sectionSubtitle {\n    font-size: 1rem;\n  }\n  \n  .cardContent {\n    padding: 1.25rem;\n  }\n  \n  .statsNumber {\n    font-size: 1.75rem;\n  }\n  \n  .categoryTitle {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .categoriesSection {\n    padding: 3rem 0;\n  }\n  \n  .container {\n    padding: 0 1rem;\n  }\n  \n  .header {\n    margin-bottom: 2.5rem;\n  }\n  \n  .categoryCard {\n    height: 280px;\n  }\n  \n  .cardContent {\n    padding: 1rem;\n  }\n  \n  .cardActions {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .actionButton,\n  .secondaryButton {\n    width: 100%;\n    justify-content: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;AAgBA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CatalogBanner/catalogBanner.module.css"], "sourcesContent": ["/* Catalog Banner Styles */\n.catalogBanner {\n  position: relative;\n  padding: 8rem 0;\n  background: #4a3728;\n  overflow: hidden;\n  margin: 4rem 0;\n}\n\n/* Background Styles */\n.backgroundContainer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1;\n}\n\n.backgroundImage {\n  object-fit: cover;\n  object-position: center;\n}\n\n.backgroundOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(74, 55, 40, 0.85) 0%,\n    rgba(74, 55, 40, 0.7) 50%,\n    rgba(107, 78, 61, 0.8) 100%\n  );\n  z-index: 2;\n}\n\n/* Content Styles */\n.container {\n  position: relative;\n  z-index: 3;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 4rem;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.textContent {\n  color: #ffffff;\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.15em;\n  color: #d4af8c;\n  margin-bottom: 1rem;\n  display: block;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3.5rem;\n  font-weight: 700;\n  line-height: 1.1;\n  color: #ffffff;\n  margin-bottom: 1.5rem;\n  letter-spacing: -0.02em;\n}\n\n.description {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2.5rem;\n  font-weight: 400;\n}\n\n/* Features */\n.features {\n  display: flex;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.feature {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.featureIcon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: rgba(212, 175, 140, 0.2);\n  border-radius: 50%;\n  color: #d4af8c;\n  flex-shrink: 0;\n}\n\n/* CTA Section */\n.ctaContainer {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.ctaButton {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.25rem 2.5rem;\n  background: linear-gradient(135deg, #d4af8c, #c19a6b);\n  color: #4a3728;\n  text-decoration: none;\n  border-radius: 50px;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(212, 175, 140, 0.3);\n}\n\n.ctaButton:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 40px rgba(212, 175, 140, 0.4);\n  background: linear-gradient(135deg, #c19a6b, #d4af8c);\n}\n\n.ctaText {\n  position: relative;\n  z-index: 2;\n}\n\n.ctaIcon {\n  position: relative;\n  z-index: 2;\n  transition: transform 0.3s ease;\n}\n\n.ctaButton:hover .ctaIcon {\n  transform: translateX(4px);\n}\n\n.buttonRipple {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\n  transform: scale(0);\n  transition: transform 0.6s ease-out;\n  z-index: 1;\n}\n\n.ctaButton:active .buttonRipple {\n  transform: scale(1);\n}\n\n/* Catalog Stats */\n.catalogStats {\n  display: flex;\n  gap: 3rem;\n}\n\n.stat {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.statNumber {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #d4af8c;\n  line-height: 1;\n  margin-bottom: 0.25rem;\n}\n\n.statLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.8);\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* Decorative Elements */\n.decorativeElements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 2;\n  pointer-events: none;\n}\n\n.decorativeCircle {\n  position: absolute;\n  top: 20%;\n  right: 10%;\n  width: 200px;\n  height: 200px;\n  border: 2px solid rgba(212, 175, 140, 0.2);\n  border-radius: 50%;\n  animation: float 6s ease-in-out infinite;\n}\n\n.decorativeLine {\n  position: absolute;\n  bottom: 20%;\n  left: 5%;\n  width: 150px;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, rgba(212, 175, 140, 0.3), transparent);\n  animation: slide 8s ease-in-out infinite;\n}\n\n/* Animations */\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes slide {\n  0%, 100% {\n    transform: translateX(0px);\n  }\n  50% {\n    transform: translateX(50px);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .catalogBanner {\n    padding: 6rem 0;\n  }\n  \n  .content {\n    gap: 3rem;\n  }\n  \n  .title {\n    font-size: 3rem;\n  }\n  \n  .features {\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .catalogBanner {\n    padding: 4rem 0;\n    margin: 3rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .content {\n    grid-template-columns: 1fr;\n    gap: 2.5rem;\n    text-align: center;\n  }\n  \n  .title {\n    font-size: 2.5rem;\n  }\n  \n  .description {\n    font-size: 1rem;\n  }\n  \n  .features {\n    justify-content: center;\n    gap: 1rem;\n  }\n  \n  .ctaContainer {\n    align-items: center;\n  }\n  \n  .catalogStats {\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .catalogBanner {\n    padding: 3rem 0;\n  }\n  \n  .container {\n    padding: 0 1rem;\n  }\n  \n  .title {\n    font-size: 2rem;\n  }\n  \n  .features {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .feature {\n    justify-content: center;\n  }\n  \n  .ctaButton {\n    padding: 1rem 2rem;\n    font-size: 0.9rem;\n  }\n  \n  .catalogStats {\n    gap: 1.5rem;\n  }\n  \n  .statNumber {\n    font-size: 2rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AASA;;;;;;AASA;;;;;AAKA;;;;;;;AAgBA;;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css"], "sourcesContent": ["/* Collections Carousel Styles */\n.collectionsSection {\n  padding: 6rem 0;\n  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Header Styles */\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  margin-bottom: 3rem;\n  gap: 2rem;\n}\n\n.headerContent {\n  flex: 1;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3rem;\n  font-weight: 700;\n  color: #4a3728;\n  margin-bottom: 1rem;\n  letter-spacing: -0.02em;\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  color: #8b7355;\n  line-height: 1.6;\n  font-weight: 400;\n  max-width: 600px;\n}\n\n/* Navigation */\n.navigation {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.navButton {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  background: #ffffff;\n  border: 2px solid rgba(74, 55, 40, 0.1);\n  border-radius: 50%;\n  color: #4a3728;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 4px 12px rgba(74, 55, 40, 0.1);\n}\n\n.navButton:hover {\n  background: #4a3728;\n  color: #ffffff;\n  border-color: #4a3728;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(74, 55, 40, 0.2);\n}\n\n/* Carousel Container */\n.carouselContainer {\n  position: relative;\n  margin-bottom: 3rem;\n}\n\n.carousel {\n  display: flex;\n  gap: 1.5rem;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  padding: 1rem 0;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.carousel::-webkit-scrollbar {\n  display: none;\n}\n\n/* Collection Card */\n.collectionCard {\n  flex: 0 0 320px;\n  height: 400px;\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #ffffff;\n  box-shadow: 0 8px 32px rgba(74, 55, 40, 0.1);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.collectionCard:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 20px 60px rgba(74, 55, 40, 0.15);\n}\n\n.cardLink {\n  display: block;\n  width: 100%;\n  height: 100%;\n  text-decoration: none;\n  color: inherit;\n}\n\n/* Image Styles */\n.imageContainer {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.collectionImage {\n  object-fit: cover;\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.collectionCard:hover .collectionImage {\n  transform: scale(1.05);\n}\n\n.imageOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.3) 0%,\n    rgba(0, 0, 0, 0.1) 50%,\n    rgba(74, 55, 40, 0.6) 100%\n  );\n  z-index: 1;\n}\n\n/* Card Content */\n.cardContent {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 2rem;\n  color: #ffffff;\n  z-index: 2;\n  background: linear-gradient(\n    to top,\n    rgba(0, 0, 0, 0.8) 0%,\n    rgba(0, 0, 0, 0.4) 50%,\n    transparent 100%\n  );\n}\n\n.productCount {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.8rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n  color: #d4af8c;\n  margin-bottom: 0.5rem;\n}\n\n.collectionName {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 0.75rem;\n  line-height: 1.2;\n}\n\n.collectionDescription {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.4;\n  margin-bottom: 1rem;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.cardAction {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  color: #d4af8c;\n  transition: all 0.3s ease;\n}\n\n.collectionCard:hover .cardAction {\n  color: #ffffff;\n  transform: translateX(4px);\n}\n\n.actionText {\n  transition: transform 0.3s ease;\n}\n\n/* View All Button */\n.viewAllContainer {\n  text-align: center;\n}\n\n.viewAllButton {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem 2rem;\n  background: transparent;\n  color: #4a3728;\n  text-decoration: none;\n  border: 2px solid #4a3728;\n  border-radius: 50px;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.viewAllButton:hover {\n  background: #4a3728;\n  color: #ffffff;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(74, 55, 40, 0.3);\n}\n\n/* Loading and Error States */\n.loadingContainer,\n.errorContainer {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #8b7355;\n}\n\n.loadingSpinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(74, 55, 40, 0.1);\n  border-top: 3px solid #4a3728;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .collectionsSection {\n    padding: 4rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .title {\n    font-size: 2.5rem;\n  }\n  \n  .collectionCard {\n    flex: 0 0 280px;\n    height: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1.5rem;\n  }\n  \n  .navigation {\n    align-self: flex-end;\n  }\n  \n  .title {\n    font-size: 2rem;\n  }\n  \n  .subtitle {\n    font-size: 1rem;\n  }\n  \n  .collectionCard {\n    flex: 0 0 260px;\n    height: 320px;\n  }\n  \n  .cardContent {\n    padding: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 1rem;\n  }\n  \n  .header {\n    margin-bottom: 2rem;\n  }\n  \n  .navigation {\n    display: none;\n  }\n  \n  .collectionCard {\n    flex: 0 0 240px;\n    height: 300px;\n  }\n  \n  .cardContent {\n    padding: 1.25rem;\n  }\n  \n  .collectionName {\n    font-size: 1.25rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;AAgBA;;;;;;;;;;;AAgBA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/TestimonialsSection/testimonialsSection.module.css"], "sourcesContent": ["/* Testimonials Section Styles */\n.testimonialsSection {\n  padding: 8rem 0;\n  background: linear-gradient(135deg, #4a3728 0%, #6b4e3d 100%);\n  color: #ffffff;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonialsSection::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('/images/testimonials-pattern.svg') repeat;\n  opacity: 0.05;\n  z-index: 1;\n}\n\n.container {\n  position: relative;\n  z-index: 2;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Header Styles */\n.header {\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.15em;\n  color: #d4af8c;\n  margin-bottom: 1rem;\n  display: block;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 3.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin-bottom: 1.5rem;\n  letter-spacing: -0.02em;\n  line-height: 1.1;\n}\n\n.description {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.125rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n/* Testimonials Container */\n.testimonialsContainer {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 4rem;\n  align-items: center;\n  margin-bottom: 4rem;\n}\n\n/* Testimonial Content */\n.testimonialContent {\n  position: relative;\n}\n\n.quoteIcon {\n  color: #d4af8c;\n  margin-bottom: 2rem;\n  opacity: 0.7;\n}\n\n.testimonialText {\n  position: relative;\n}\n\n.testimonialContent {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  line-height: 1.6;\n  color: #ffffff;\n  margin-bottom: 2rem;\n  font-style: italic;\n  font-weight: 400;\n}\n\n.rating {\n  display: flex;\n  gap: 0.25rem;\n  margin-bottom: 1.5rem;\n}\n\n.projectInfo {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n}\n\n.projectLabel {\n  color: #d4af8c;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.projectName {\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n/* Testimonial Meta */\n.testimonialMeta {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.authorInfo {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n\n.authorImage {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  overflow: hidden;\n  border: 3px solid #d4af8c;\n  flex-shrink: 0;\n}\n\n.authorPhoto {\n  object-fit: cover;\n}\n\n.authorDetails {\n  flex: 1;\n}\n\n.authorName {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 0.25rem;\n  line-height: 1.2;\n}\n\n.authorTitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  color: #d4af8c;\n  font-weight: 500;\n  margin-bottom: 0.125rem;\n}\n\n.authorCompany {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 400;\n}\n\n/* Navigation */\n.navigation {\n  display: flex;\n  gap: 0.75rem;\n  justify-content: center;\n}\n\n.navDot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.navDot:hover {\n  background: rgba(212, 175, 140, 0.7);\n  transform: scale(1.2);\n}\n\n.navDot.active {\n  background: #d4af8c;\n  transform: scale(1.3);\n}\n\n/* Stats */\n.stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 2rem;\n  padding-top: 3rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.stat {\n  text-align: center;\n}\n\n.statNumber {\n  display: block;\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #d4af8c;\n  line-height: 1;\n  margin-bottom: 0.5rem;\n}\n\n.statLabel {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.85rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .testimonialsSection {\n    padding: 6rem 0;\n  }\n  \n  .container {\n    padding: 0 1.5rem;\n  }\n  \n  .title {\n    font-size: 3rem;\n  }\n  \n  .testimonialsContainer {\n    gap: 3rem;\n  }\n  \n  .testimonialContent {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .testimonialsSection {\n    padding: 4rem 0;\n  }\n  \n  .header {\n    margin-bottom: 3rem;\n  }\n  \n  .title {\n    font-size: 2.5rem;\n  }\n  \n  .description {\n    font-size: 1rem;\n  }\n  \n  .testimonialsContainer {\n    grid-template-columns: 1fr;\n    gap: 2.5rem;\n    text-align: center;\n  }\n  \n  .testimonialContent {\n    font-size: 1.125rem;\n  }\n  \n  .authorInfo {\n    justify-content: center;\n  }\n  \n  .stats {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 1rem;\n  }\n  \n  .header {\n    margin-bottom: 2rem;\n  }\n  \n  .title {\n    font-size: 2rem;\n  }\n  \n  .testimonialContent {\n    font-size: 1rem;\n  }\n  \n  .authorInfo {\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n  \n  .authorImage {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .stats {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .statNumber {\n    font-size: 2rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;AAYA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/homeComponent.module.css"], "sourcesContent": ["/* Home Component Styles */\n.homeComponent {\n  min-height: 100vh;\n  background: #ffffff;\n  overflow-x: hidden;\n}\n\n/* Smooth scrolling for the entire page */\n.homeComponent {\n  scroll-behavior: smooth;\n}\n\n/* Ensure proper spacing between sections */\n.homeComponent > * {\n  position: relative;\n  z-index: 1;\n}\n\n/* Add subtle transitions for section reveals */\n.homeComponent section {\n  opacity: 1;\n  transform: translateY(0);\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .homeComponent {\n    overflow-x: hidden;\n  }\n}\n\n/* Print styles */\n@media print {\n  .homeComponent {\n    background: white;\n    color: black;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAYA;;;;;AAMA;;;;;;AAOA;EACE;;;;;AAMF;EACE", "debugId": null}}]}